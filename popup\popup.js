// Adicionar variáveis para rastrear o último checkbox clicado de pastas
let lastClickedFolderCheckbox = null;

// Tornar as variáveis acessíveis globalmente
window.lastClickedFolderCheckbox = lastClickedFolderCheckbox;

const folderCheckboxesContainer = document.getElementById("folderCheckboxes");
const bookmarksContainer = document.getElementById("bookmarksContainer");
const selectedFolderIds = new Set();
// selectedBookmarkIds agora está em selection.js

// Mapeia favoritos por pasta para poder remover depois
const folderBookmarkMap = new Map();

// Configurações de monitoramento de desempenho
const PERFORMANCE_CHECK_INTERVAL = 30000; // 30 segundos
const CACHE_CLEANUP_INTERVAL = 120000; // 2 minutos

// No início do arquivo, adicionar variável para as configurações
let extensionSettings = {
  confirmDelete: true,
  confirmMerge: true,
  theme: 'system',
  fontSize: 'medium',
};

// Listener para mensagens de atualização de configuração
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'configUpdated' && message.settings) {
    console.log('Recebida atualização de configurações:', message.settings);

    // Atualizar configurações
    extensionSettings = {
      ...extensionSettings,
      ...message.settings
    };

    // Aplicar novas configurações imediatamente
    applyThemeFromSettings();

    // Responder confirmando o recebimento
    sendResponse({ success: true });
  }

  // Listener específico para atualizações de tema
  if (message.action === 'themeUpdated' && message.themeSettings) {
    console.log('Recebida atualização de tema:', message.themeSettings);

    // Atualizar configurações de tema
    extensionSettings.themeSettings = message.themeSettings;

    // Remover estilos dinâmicos existentes antes de aplicar novos
    const existingStyleTag = document.getElementById('dynamic-theme-styles');
    if (existingStyleTag) {
      existingStyleTag.remove();
    }

    // Aplicar temas atualizados imediatamente
    applyThemeFromSettings();

    // Aplicar novamente após um pequeno delay para garantir
    setTimeout(() => {
      applyThemeFromSettings();
    }, 50);

    sendResponse({ success: true });
  }

  return true; // Para manter o canal de comunicação aberto para respostas assíncronas
});

document.addEventListener("DOMContentLoaded", async () => {
  // Carregar configurações antes de qualquer operação
  await loadSettings();

  // Aplicar tema com base nas configurações carregadas
  applyThemeFromSettings();

  // Garantir que os temas sejam aplicados após um pequeno delay (fallback)
  setTimeout(() => {
    applyThemeFromSettings();
  }, 100);

  // Garantir que não haja nenhum estado residual
  selectedFolderIds.clear();
  folderBookmarkMap.clear();

  // Limpar contêiner de favoritos e zerar contador
  bookmarksContainer.innerHTML = '';
  document.getElementById("bookmarksDisplayCount").textContent = "Favoritos exibidos: 0";

  // Inicializar sistema de seleção de favoritos
  initBookmarkSelection();

  // Configurar seleção com Shift para pastas
  setupFolderShiftSelection();

  // Inicializar sistema de drag and drop
  initDragAndDrop();

  // Inicializar o redimensionamento das colunas
  initializeResizableSeparator();

  // Carregar a árvore de pastas
  chrome.bookmarks.getTree((tree) => {
    const roots = tree[0].children;
    populateFolderCheckboxes(roots, folderCheckboxesContainer);
    updateFolderCount();

    // Aplicar temas novamente após a interface estar completamente carregada
    setTimeout(() => {
      console.log('Reaplicando temas após carregamento completo da interface...');
      applyThemeFromSettings();
    }, 200);
  });

  // Monitor global para verificar se o estado da aplicação está consistente
  const consistencyChecker = setInterval(() => {
    // Se não há pastas selecionadas, não deve haver favoritos visíveis
    if (selectedFolderIds.size === 0 && bookmarksContainer.children.length > 0) {
      console.log("Detectada inconsistência: favoritos visíveis sem pastas selecionadas, limpando...");
      clearAllBookmarks();
    }
    
    // Verificar se o contador de favoritos está consistente com os elementos visíveis
    const countEl = document.getElementById("bookmarksDisplayCount");
    const countText = countEl.textContent;
    const countMatch = countText.match(/\d+/);
    const displayedCount = countMatch ? parseInt(countMatch[0], 10) : 0;
    
    const visibleCount = Array.from(bookmarksContainer.querySelectorAll('.bookmark-item'))
      .filter(item => item.style.display !== 'none').length;
    
    // Se o contador mostra zero, mas há elementos visíveis, há inconsistência
    if (displayedCount === 0 && visibleCount > 0) {
      console.log("Detectada inconsistência: contador zero com favoritos visíveis, limpando...");
      clearAllBookmarks();
    }
  }, 1000);
  
  // Monitor de desempenho para verificar o uso de memória e CPU
  const performanceMonitor = setInterval(() => {
    // Verificar disponibilidade de memória
    checkMemoryAvailability();
    
    // Registrar estatísticas de elementos no DOM
    const totalElements = document.querySelectorAll('*').length;
    const bookmarkItems = document.querySelectorAll('.bookmark-item').length;
    const folderItems = document.querySelectorAll('.folder-option').length;
    
    console.log(`Estatísticas DOM: ${totalElements} elementos totais, ${bookmarkItems} favoritos, ${folderItems} pastas`);
    
    // Verificar se há muitos elementos no DOM e considerar otimização
    if (bookmarkItems > 500) {
      console.warn("Grande quantidade de favoritos detectada, considerando otimização...");
      
      // Se houver muitos favoritos visíveis, verificar se há pastas com muitos itens
      const folderCounts = new Map();
      document.querySelectorAll('.bookmark-item').forEach(item => {
        const folderId = item.dataset.folder;
        if (folderId) {
          folderCounts.set(folderId, (folderCounts.get(folderId) || 0) + 1);
        }
      });
      
      // Identificar pastas com muitos favoritos
      for (const [folderId, count] of folderCounts.entries()) {
        if (count > 200) {
          console.warn(`Pasta ${folderId} tem ${count} favoritos visíveis, considere otimizar`);
        }
      }
    }
  }, PERFORMANCE_CHECK_INTERVAL);
  
  // Limpeza periódica de cache para evitar vazamentos de memória
  const cacheCleanupTimer = setInterval(() => {
    console.log("Executando limpeza periódica de cache...");
    pruneCache();
  }, CACHE_CLEANUP_INTERVAL);
  
  // Limpar os intervalos se a janela for fechada
  window.addEventListener('beforeunload', () => {
    clearInterval(consistencyChecker);
    clearInterval(performanceMonitor);
    clearInterval(cacheCleanupTimer);
  });

  // Configurar pesquisa de pastas
  document.getElementById("folderSearch").addEventListener("input", (e) => {
    const term = e.target.value.toLowerCase();
    const items = document.querySelectorAll(".folder-option");
    
    // Contagem de pastas visíveis após filtragem
    let visibleCount = 0;
    
    items.forEach((item) => {
      const text = item.textContent.toLowerCase();
      const isVisible = text.includes(term);
      item.style.display = isVisible ? "" : "none";
      if (isVisible) visibleCount++;
    });
    
    updateFolderCount();

    // Atualizar texto do botão "Selecionar" baseado na filtragem
    const selectAllButton = document.getElementById("selectAllFoldersBtn");
    // Atualizar texto do botão "Desselecionar" baseado na filtragem
    const deselectAllButton = document.getElementById("deselectAllFoldersBtn");
    
    if (term && visibleCount > 0) {
      selectAllButton.textContent = `Selecionar ${visibleCount} filtradas`;
      deselectAllButton.textContent = `Desselecionar ${visibleCount} filtradas`;
    } else {
      selectAllButton.textContent = "Selecionar";
      deselectAllButton.textContent = "Desselecionar";
    }
  });

  // Configurar pesquisa de favoritos
  document.getElementById("bookmarkSearch").addEventListener("input", (e) => {
    const term = e.target.value.toLowerCase();
    
    // Se não há pastas selecionadas, não fazer nada
    if (selectedFolderIds.size === 0) {
      return;
    }
    
    const items = document.querySelectorAll(".bookmark-item");
    let visibleCount = 0;
    
    items.forEach((item) => {
      const text = item.textContent.toLowerCase();
      const isVisible = text.includes(term);
      item.style.display = isVisible ? "" : "none";
      if (isVisible) visibleCount++;
    });
    
    updateBookmarkCount();
    
    // Atualizar texto dos botões de seleção de favoritos
    const selectAllButton = document.getElementById("selectAllBookmarksBtn");
    const deselectAllButton = document.getElementById("deselectAllBookmarksBtn");
    
    if (term && visibleCount > 0) {
      selectAllButton.textContent = `Selecionar ${visibleCount} filtrados`;
      deselectAllButton.textContent = `Desselecionar ${visibleCount} filtrados`;
    } else {
      selectAllButton.textContent = "Selecionar";
      deselectAllButton.textContent = "Desselecionar";
    }
  });

  // Configurar botões do rodapé
  document.getElementById("mergeBtn").addEventListener("click", handleMergeFolders);
  
  // Novo código para dropdown do botão de ordenação
  const sortBtn = document.getElementById("sortBtn");
  const sortDropdown = document.getElementById("sortDropdown");
  
  // Configurar clique no botão de ordenação para abrir dropdown
  sortBtn.addEventListener("click", (e) => {
    e.stopPropagation();
    sortDropdown.classList.toggle("show");
    sortBtn.classList.toggle("active");
  });
  
  // Configurar opções do dropdown
  document.getElementById("sortByTitle").addEventListener("click", () => {
    sortDropdown.classList.remove("show");
    sortBtn.classList.remove("active");
    handleSortBookmarksWithType("title");
  });
  
  document.getElementById("sortByUrl").addEventListener("click", () => {
    sortDropdown.classList.remove("show");
    sortBtn.classList.remove("active");
    handleSortBookmarksWithType("url");
  });
  
  document.getElementById("sortByDomain").addEventListener("click", () => {
    sortDropdown.classList.remove("show");
    sortBtn.classList.remove("active");
    handleSortBookmarksWithType("domain");
  });
  
  document.getElementById("sortByDateAdded").addEventListener("click", () => {
    sortDropdown.classList.remove("show");
    sortBtn.classList.remove("active");
    handleSortBookmarksWithType("dateAdded");
  });
  
  document.getElementById("sortByDateModified").addEventListener("click", () => {
    sortDropdown.classList.remove("show");
    sortBtn.classList.remove("active");
    handleSortBookmarksWithType("dateModified");
  });
  
  // Fechar dropdown quando clicar fora dele
  document.addEventListener("click", (e) => {
    if (!e.target.matches('#sortBtn') && !sortDropdown.contains(e.target)) {
      sortDropdown.classList.remove("show");
      sortBtn.classList.remove("active");
    }
  });
  
  document.getElementById("deleteBtn").addEventListener("click", handleDeleteSelectedBookmarks);

  // Configurar o botão de copiar e seu dropdown
  const copyBtn = document.getElementById("copyBtn");
  const copyDropdown = document.getElementById("copyDropdown");
  
  // Configurar clique no botão de copiar para abrir dropdown
  copyBtn.addEventListener("click", (e) => {
    e.stopPropagation();
    copyDropdown.classList.toggle("show");
    copyBtn.classList.toggle("active");
  });
  
  // Configurar opções do dropdown de cópia
  document.getElementById("copyTitles").addEventListener("click", () => {
    copyDropdown.classList.remove("show");
    copyBtn.classList.remove("active");
    handleCopySelectedTitles();
  });
  
  document.getElementById("copyUrls").addEventListener("click", () => {
    copyDropdown.classList.remove("show");
    copyBtn.classList.remove("active");
    handleCopySelectedUrls();
  });
  
  // Fechar dropdown quando clicar fora dele (junto com o dropdown de classificação)
  document.addEventListener("click", (e) => {
    if (!e.target.matches('#sortBtn') && !sortDropdown.contains(e.target)) {
      sortDropdown.classList.remove("show");
      sortBtn.classList.remove("active");
    }
    if (!e.target.matches('#copyBtn') && !copyDropdown.contains(e.target)) {
      copyDropdown.classList.remove("show");
      copyBtn.classList.remove("active");
    }
  });

  // Configurar o botão "Selecionar todas" para pastas
  document.getElementById("selectAllFoldersBtn").addEventListener("click", () => {
    toggleAllFolders(true);
  });

  // Configurar o botão "Desselecionar" para pastas
  document.getElementById("deselectAllFoldersBtn").addEventListener("click", () => {
    // Obter referência ao botão
    const deselectBtn = document.getElementById("deselectAllFoldersBtn");
    
    // Verificar se o botão já está em processamento para evitar cliques múltiplos
    if (deselectBtn.classList.contains('processing')) return;
    
    // Adicionar classe para feedback visual
    deselectBtn.classList.add("processing");
    deselectBtn.textContent = "Processando...";
    
    // Contar quantas pastas filtradas estão sendo desselecionadas
    const visibleFoldersCount = getVisibleFoldersCount();
    const selectedCount = selectedFolderIds.size;
    
    // Mostrar feedback para o usuário
    showActionFeedback("Pausando carregamento de favoritos...", "info");
    
    // Primeiro, pausar explicitamente o procedimento de carregamento
    // Atualizar flag global para bloquear todas as renderizações
    blockRendering = true;
    
    // Limpar renderizações pendentes de forma mais eficiente
    if (window.folderBatchTimeout) {
      clearTimeout(window.folderBatchTimeout);
      window.folderBatchTimeout = null;
    }
    
    // Limpar quaisquer outros timeouts relacionados a carregamento de favoritos
    const existingTimeouts = window.setTimeout(() => {}, 0);
    for (let i = 0; i < existingTimeouts; i++) {
      window.clearTimeout(i);
    }
    
    console.log("Carregamento de favoritos pausado com sucesso");
    
    // Espera um breve momento para garantir que todos os carregamentos foram interrompidos
    setTimeout(() => {
      // Agora mostra feedback de que está desselecionando com número de pastas
      const feedbackMsg = selectedCount > 0 
        ? `Desselecionando ${selectedCount}/${visibleFoldersCount} pastas...` 
        : "Nenhuma pasta selecionada para desselecionar";
      
      showActionFeedback(feedbackMsg, selectedCount > 0 ? "info" : "warning");
      
      // Executar desseleção
      toggleAllFolders(false);
      
      // Restaurar aparência normal do botão após conclusão
      // Usando o técnica de requestAnimationFrame para garantir que a UI seja atualizada
      requestAnimationFrame(() => {
        setTimeout(() => {
          if (deselectBtn) {
            // Forçar o reflow/repaint do botão para garantir que o estado visual seja atualizado
            deselectBtn.classList.remove("processing");
            deselectBtn.style.display = 'none';
            deselectBtn.offsetHeight; // Força o reflow
            deselectBtn.style.display = '';
            deselectBtn.textContent = "Desselecionar";
            deselectBtn.blur(); // Remove qualquer foco ativo no botão
          }
          
          // Remover o feedback após a operação ser concluída
          const feedbackElement = document.querySelector('.action-feedback');
          if (feedbackElement) {
            feedbackElement.classList.add('hide');
            setTimeout(() => {
              if (feedbackElement.parentNode) {
                feedbackElement.remove();
              }
            }, 300);
          }
          
          // Garantir que a renderização seja desbloqueada após a operação
          blockRendering = false;
        }, 300);
      });
    }, 200); // Espera 200ms antes de iniciar a desseleção
  });
  
  // Configurar clique direito para seleção de pastas
  configureFolderRightClickSelection();

  // Os botões de seleção de favoritos e clique direito agora estão em selection.js

  document.getElementById("duplicateBtn").addEventListener("click", handleDuplicateSelectedBookmarks);
});

/**
 * Inicializa a funcionalidade de redimensionamento do separador.
 * Permite arrastar o separador para ajustar o tamanho da coluna de favoritos,
 * permitindo que ela sobreponha a coluna de pastas.
 */
function initializeResizableSeparator() {
  const separator = document.getElementById('mainSeparator');
  const foldersColumn = document.querySelector('.folders-column');
  const bookmarksColumn = document.querySelector('.bookmarks-column');
  const container = document.querySelector('.content-container');
  
  let isResizing = false;
  let startX, startLeft;
  let currentLeft = 0;
  let ticking = false;
  
  const STORAGE_KEY = 'separator_position';
  
  // Valores mínimos e máximos para a posição do separador
  const minLeft = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--separator-min-left')) || 100;
  const maxOverlap = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--max-overlap')) || 150;
  const containerWidth = container.clientWidth;
  const maxLeft = containerWidth - parseInt(getComputedStyle(document.documentElement).getPropertyValue('--min-column-width')) || containerWidth - 200;
  
  // Definir separador na posição inicial
  // Posição padrão: 40% da largura do container (mais à esquerda que o meio)
  let initialLeft = Math.floor(containerWidth * 0.4);

  // Carregar posição salva inicialmente
  try {
    const savedPosition = localStorage.getItem(STORAGE_KEY);
    if (savedPosition) {
      initialLeft = parseFloat(savedPosition);
      initialLeft = Math.min(Math.max(initialLeft, minLeft), maxLeft);
    }
  } catch(err) {
    console.warn('Erro ao carregar posição salva:', err);
  }
  
  // Configurar posições iniciais
  separator.style.left = `${initialLeft}px`;
  bookmarksColumn.style.left = `${initialLeft}px`;
  bookmarksColumn.style.width = `${containerWidth - initialLeft}px`;
  foldersColumn.style.width = `${initialLeft}px`; // Largura da coluna de pastas igual à posição do separador
  currentLeft = initialLeft;
  
  // Função para centralizar o separador com duplo-clique
  function resetSeparatorToMiddle() {
    const containerWidth = container.clientWidth;
    const middlePosition = Math.floor(containerWidth / 2);
    
    // Aplicar posições diretamente, sem transições
    separator.style.transition = 'none';
    bookmarksColumn.style.transition = 'none';
    
    separator.style.left = `${middlePosition}px`;
    bookmarksColumn.style.left = `${middlePosition}px`;
    bookmarksColumn.style.width = `${containerWidth - middlePosition}px`;
    foldersColumn.style.width = `${middlePosition}px`; // Atualizar largura da coluna de pastas

    currentLeft = middlePosition;
    
    // Salvar a nova posição
    try {
      localStorage.setItem(STORAGE_KEY, middlePosition.toString());
    } catch(err) {
      console.warn('Erro ao salvar posição:', err);
    }
    
    // Efeito visual de feedback sem animação
    separator.classList.add('reset-effect');
    setTimeout(() => {
      separator.classList.remove('reset-effect');
    }, 200);
    
    // Atualizar atributo aria-valuenow para acessibilidade
    const ratio = Math.round((middlePosition / containerWidth) * 100);
    separator.setAttribute('aria-valuenow', ratio);
  }
  
  // Função para redimensionar com teclado
  function resizeWithKeyboard(direction) {
    const increment = direction === 'left' ? -20 : 20;
    const newLeft = Math.min(Math.max(currentLeft + increment, minLeft), maxLeft);
    
    // Aplicar nova posição diretamente, sem transições
    separator.style.transition = 'none';
    bookmarksColumn.style.transition = 'none';
    
    separator.style.left = `${newLeft}px`;
    bookmarksColumn.style.left = `${newLeft}px`;
    bookmarksColumn.style.width = `${containerWidth - newLeft}px`;
    foldersColumn.style.width = `${newLeft}px`; // Atualizar largura da coluna de pastas

    currentLeft = newLeft;
    
    // Salvar a nova posição
    try {
      localStorage.setItem(STORAGE_KEY, newLeft.toString());
    } catch(err) {
      console.warn('Erro ao salvar posição:', err);
    }
    
    // Feedback visual temporário sem animações
    separator.classList.add('key-resize');
    setTimeout(() => {
      separator.classList.remove('key-resize');
    }, 150);
    
    // Atualizar atributo aria-valuenow para acessibilidade
    const ratio = Math.round((newLeft / containerWidth) * 100);
    separator.setAttribute('aria-valuenow', ratio);
  }

  // Função para criar o ghost do separador
  function createGhost() {
    const existing = document.getElementById('separator-ghost');
    if (existing) existing.remove();
    
    // Calcular altura considerando o footer
    const footerHeight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--footer-height')) || 60;
    const mainHeight = container.clientHeight;
    
    const ghost = document.createElement('div');
    ghost.id = 'separator-ghost';
    ghost.style.position = 'absolute';
    ghost.style.top = '0';
    ghost.style.left = `${startLeft}px`;
    ghost.style.height = `calc(100% - ${footerHeight}px)`;
    ghost.style.width = '2px';
    ghost.style.zIndex = '20'; // Menor que o z-index do footer (100)
    ghost.style.pointerEvents = 'none';
    ghost.style.backgroundColor = 'var(--accent, #0078D4)';
    ghost.style.opacity = '1';
    ghost.style.bottom = `${footerHeight}px`; // Parar antes do footer
    document.body.appendChild(ghost);
    
    return ghost;
  }
  
  // Evento de mouse down para iniciar o redimensionamento
  separator.addEventListener('mousedown', (e) => {
    e.preventDefault();
    if (isResizing) return; // Evitar inicializações múltiplas
    
    isResizing = true;
    startX = e.clientX;
    startLeft = parseInt(getComputedStyle(separator).left, 10);
    
    separator.classList.add('dragging');
    document.body.classList.add('resizing');
    
    // Remover qualquer transição para mover tudo imediatamente
    separator.style.transition = 'none';
    bookmarksColumn.style.transition = 'none';
    
    const ghost = createGhost();
    
    function onMove(ev) {
      if (!isResizing) return;
      
      const clientX = ev.clientX || (ev.touches && ev.touches[0].clientX);
      const newLeft = Math.min(Math.max(startLeft + (clientX - startX), minLeft), maxLeft);
      
      // Mover apenas o ghost durante o arrasto para performance
      ghost.style.left = `${newLeft}px`;
      
      // Mover o separador e redimensionar as colunas diretamente sem animações
      separator.style.left = `${newLeft}px`;
      bookmarksColumn.style.left = `${newLeft}px`;
      bookmarksColumn.style.width = `${containerWidth - newLeft}px`;
      foldersColumn.style.width = `${newLeft}px`; // Atualizar largura da coluna de pastas

      currentLeft = newLeft;
      
      // Atualizar atributo aria-valuenow para acessibilidade
      const ratio = Math.round((newLeft / containerWidth) * 100);
      separator.setAttribute('aria-valuenow', ratio);
    }
    
    function onUp() {
      if (!isResizing) return;
      
      isResizing = false;
    separator.classList.remove('dragging');
    document.body.classList.remove('resizing');
      
      try { 
        localStorage.setItem(STORAGE_KEY, currentLeft.toString()); 
      } catch(err) {
        console.warn('Erro ao salvar posição:', err);
      }
      
      // Remover o ghost imediatamente
        ghost.remove(); 
      
      document.removeEventListener('mousemove', onMove);
      document.removeEventListener('touchmove', onMove);
      document.removeEventListener('mouseup', onUp);
      document.removeEventListener('touchend', onUp);
    }

    document.addEventListener('mousemove', onMove);
    document.addEventListener('touchmove', onMove);
    document.addEventListener('mouseup', onUp);
    document.addEventListener('touchend', onUp);
  });
  
  // Adicionar evento de duplo-clique para centralizar o separador
  separator.addEventListener('dblclick', resetSeparatorToMiddle);
  
  // Adicionar suporte para navegação por teclado
  separator.addEventListener('keydown', (e) => {
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        resizeWithKeyboard('left');
        break;
      case 'ArrowRight':
        e.preventDefault();
        resizeWithKeyboard('right');
        break;
      case 'Home':
        e.preventDefault();
        // Ir para posição mínima
        const minPosition = minLeft;
        
        separator.style.transition = 'none';
        bookmarksColumn.style.transition = 'none';
        
        separator.style.left = `${minPosition}px`;
        bookmarksColumn.style.left = `${minPosition}px`;
        bookmarksColumn.style.width = `${containerWidth - minPosition}px`;
        foldersColumn.style.width = `${minPosition}px`; // Atualizar largura da coluna de pastas

        currentLeft = minPosition;
        
        try { 
          localStorage.setItem(STORAGE_KEY, currentLeft.toString()); 
        } catch(err) {
          console.warn('Erro ao salvar posição:', err);
        }
        
        separator.setAttribute('aria-valuenow', Math.round((minPosition / containerWidth) * 100));
        break;
      case 'End':
        e.preventDefault();
        // Ir para posição máxima
        const maxPosition = maxLeft;
        
        separator.style.transition = 'none';
        bookmarksColumn.style.transition = 'none';
        
        separator.style.left = `${maxPosition}px`;
        bookmarksColumn.style.left = `${maxPosition}px`;
        bookmarksColumn.style.width = `${containerWidth - maxPosition}px`;
        foldersColumn.style.width = `${maxPosition}px`; // Atualizar largura da coluna de pastas

        currentLeft = maxPosition;
        
        try { 
          localStorage.setItem(STORAGE_KEY, currentLeft.toString()); 
        } catch(err) {
          console.warn('Erro ao salvar posição:', err);
        }
        
        separator.setAttribute('aria-valuenow', Math.round((maxPosition / containerWidth) * 100));
        break;
      case ' ':
      case 'Enter':
        e.preventDefault();
        resetSeparatorToMiddle();
        break;
    }
  });
  
  // Recalcular em caso de redimensionamento da janela
  window.addEventListener('resize', () => {
    const containerWidth = container.clientWidth;
    const maxLeft = containerWidth - 200;
    const newLeft = Math.min(Math.max(currentLeft, minLeft), maxLeft);

    separator.style.left = `${newLeft}px`;
    bookmarksColumn.style.left = `${newLeft}px`;
    bookmarksColumn.style.width = `${containerWidth - newLeft}px`;
    foldersColumn.style.width = `${newLeft}px`; // Atualizar largura da coluna de pastas

    currentLeft = newLeft;
  });
  
  // Definir o valor inicial de aria-valuenow
  const initialRatio = Math.round((initialLeft / containerWidth) * 100);
  separator.setAttribute('aria-valuenow', initialRatio);
}



// updateSelectedBookmarksCount agora está em selection.js

// Função para carregar configurações da extensão
async function loadSettings() {
  return new Promise(resolve => {
    chrome.storage.sync.get(['settings', 'themeSettings'], (result) => {
        if (chrome.runtime.lastError) {
        console.warn('Erro ao acessar storage.sync, tentando storage.local:', chrome.runtime.lastError);
        chrome.storage.local.get(['settings', 'themeSettings'], (localResult) => {
          extensionSettings = { ...extensionSettings, ...(localResult.settings || {}), themeSettings: localResult.themeSettings };
          console.log('Configurações carregadas (local):', extensionSettings);
          resolve();
          });
        } else {
        extensionSettings = { ...extensionSettings, ...(result.settings || {}), themeSettings: result.themeSettings };
        console.log('Configurações carregadas (sync):', extensionSettings);
        resolve();
        }
      });
    });
}

function hexToRgba(hex, opacity) {
    let r = 0, g = 0, b = 0;
    if (hex.length == 4) {
        r = "0x" + hex[1] + hex[1];
        g = "0x" + hex[2] + hex[2];
        b = "0x" + hex[3] + hex[3];
    } else if (hex.length == 7) {
        r = "0x" + hex[1] + hex[2];
        g = "0x" + hex[3] + hex[4];
        b = "0x" + hex[5] + hex[6];
    }
    return `rgba(${+r},${+g},${+b},${opacity})`;
}

function generateAndApplyThemeStyles() {
    const themeName = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
    const themeSettings = extensionSettings.themeSettings;

    console.log('Aplicando tema:', themeName);
    console.log('Configurações de tema disponíveis:', themeSettings);

    if (!themeSettings) {
        console.log("Nenhuma configuração de tema encontrada, usando estilos padrão.");
        return;
    }

    if (!themeSettings[themeName]) {
        console.log("Configurações de tema personalizadas não encontradas para", themeName);
        // Tentar aplicar configurações do tema oposto se existir
        const fallbackTheme = themeName === 'dark' ? 'light' : 'dark';
        if (themeSettings[fallbackTheme]) {
            console.log("Usando configurações do tema", fallbackTheme, "como fallback");
            // Não aplicar fallback por enquanto, apenas usar estilos padrão
        }
        return;
    }

    const theme = themeSettings[themeName];
    let css = '<style id="dynamic-theme-styles">\n';

    // Normal State
    const normal = theme.states.normal;
    let normalStyles = '';
    if (normal.background.enabled) normalStyles += `background-color: ${hexToRgba(normal.background.color, normal.background.opacity)} !important;\n`;
    if (normal.border.enabled) normalStyles += `border-bottom: ${normal.border.thickness}px solid ${hexToRgba(normal.border.color, normal.border.opacity)} !important;\n`;
    if (normal.shadow.enabled) normalStyles += `box-shadow: 0 ${normal.shadow.size}px ${normal.shadow.size * 2}px ${hexToRgba(normal.shadow.color, normal.shadow.opacity)} !important;\n`;
    css += `.folder-option, .bookmark-item { ${normalStyles} }\n`;

    // Selected State
    const selected = theme.states.selected;
    let selectedStyles = '';
    if (selected.background.enabled) selectedStyles += `background-color: ${hexToRgba(selected.background.color, selected.background.opacity)} !important;\n`;
    if (selected.border.enabled) selectedStyles += `border-bottom: ${selected.border.thickness}px solid ${hexToRgba(selected.border.color, selected.border.opacity)} !important;\n`;
    if (selected.shadow.enabled) selectedStyles += `box-shadow: 0 ${selected.shadow.size}px ${selected.shadow.size * 2}px ${hexToRgba(selected.shadow.color, selected.shadow.opacity)} !important;\n`;
    css += `.folder-option.selected, .bookmark-item.selected { ${selectedStyles} }\n`;
    
    // Hover State
    const hover = theme.states.hover;
    let hoverStyles = '';
    if (hover.background.enabled) hoverStyles += `background-color: ${hexToRgba(hover.background.color, hover.background.opacity)} !important;\n`;
    if (hover.border.enabled) hoverStyles += `border-bottom: ${hover.border.thickness}px solid ${hexToRgba(hover.border.color, hover.border.opacity)} !important;\n`;
    if (hover.shadow.enabled) hoverStyles += `box-shadow: 0 ${hover.shadow.size}px ${hover.shadow.size * 2}px ${hexToRgba(hover.shadow.color, hover.shadow.opacity)} !important;\n`;

    css += `.folder-option:hover, .bookmark-item:hover { ${hoverStyles} }\n`;

    // Aplicar underline APENAS no hover específico do title-container
    if (hover.underline && hover.underline.enabled) {
        let underlineStyles = '';
        underlineStyles += `text-decoration: underline !important;\n`;
        underlineStyles += `text-decoration-thickness: ${hover.underline.thickness}px !important;\n`;
        underlineStyles += `text-decoration-color: ${hexToRgba(hover.underline.color, hover.underline.opacity)} !important;\n`;

        // Aplicar underline apenas no hover específico do title-container
        css += `.title-container:hover .bookmark-link { ${underlineStyles} }\n`;
        css += `.folder-option:hover .folder-title { ${underlineStyles} }\n`;
    } else {
        // Garantir que não há underline quando desabilitado
        css += `.title-container:hover .bookmark-link { text-decoration: none !important; }\n`;
        css += `.folder-option:hover .folder-title { text-decoration: none !important; }\n`;
    }

    // Garantir que outros elementos nunca tenham underline
    css += `.bookmark-item:hover .url-container { text-decoration: none !important; }\n`;
    css += `.bookmark-item:hover { text-decoration: none !important; }\n`;

    css += '</style>';

    const existingStyleTag = document.getElementById('dynamic-theme-styles');
    if (existingStyleTag) {
        existingStyleTag.remove();
    }
    document.head.insertAdjacentHTML('beforeend', css);

    console.log('Estilos de tema personalizados aplicados para:', themeName);
}

function applyThemeFromSettings() {
  const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const currentTheme = extensionSettings.theme;

  if (currentTheme === 'dark' || (currentTheme === 'system' && isDarkMode)) {
    document.body.classList.add('dark-theme');
  } else {
    document.body.classList.remove('dark-theme');
  }
  
  // Aplicar estilos personalizados
  generateAndApplyThemeStyles();

  // Atualizar altura
  const itemHeight = extensionSettings.fontSize === 'small' ? '30px' : '35px';
  
  const style = document.createElement('style');
  style.textContent = `
    .folder-option, .bookmark-item {
      height: ${itemHeight};
    }
  `;
  document.head.appendChild(style);
}

// Modificar a função handleDeleteSelectedBookmarks para usar as configurações
function handleDeleteSelectedBookmarks() {
  const selectedIds = getSelectedBookmarkIds();

  if (selectedIds.length === 0) {
    showActionFeedback("Nenhum favorito selecionado para exclusão.", "error");
    return;
  }

  // Verificar configuração de confirmação antes de excluir
  if (extensionSettings.confirmDelete) {
  const confirmMessage = `Deseja excluir ${selectedIds.length} favorito(s) selecionado(s)?`;
  if (!confirm(confirmMessage)) return;
  }

  const deletePromises = selectedIds.map(id =>
    new Promise((resolve) => {
      chrome.bookmarks.remove(id, () => {
        const item = bookmarksContainer.querySelector(`[data-id="${id}"]`);
        if (item) item.remove();
        resolve();
      });
    })
  );

  Promise.all(deletePromises).then(() => {
    clearBookmarkSelection();
    // Usar a função correta do events.js
    if (typeof window.reloadSelectedFolders === 'function') {
      window.reloadSelectedFolders();
    } else if (typeof reloadSelectedFolders === 'function') {
      reloadSelectedFolders();
    } else {
      // Fallback: limpar e recarregar bookmarks das pastas selecionadas
      bookmarksContainer.innerHTML = '';
      selectedFolderIds.forEach(folderId => {
        chrome.bookmarks.getChildren(folderId, (children) => {
          if (!chrome.runtime.lastError) {
            const items = children.filter(c => c.url);
            renderBookmarks(items, bookmarksContainer, false, folderId, false);
          }
        });
      });
    }
    updateBookmarkCount();
    if (typeof updateSelectedBookmarksCount === 'function') {
      updateSelectedBookmarksCount();
    }
    showActionFeedback("Favoritos excluídos com sucesso!", "success");
  });
}

/**
 * Obtém a contagem de pastas filtradas visíveis na interface
 * @returns {number} Número de pastas visíveis após filtragem
 */
function getVisibleFoldersCount() {
  const visibleItems = document.querySelectorAll(".folder-option");
  return Array.from(visibleItems).filter(item => item.style.display !== "none").length;
}

// Atualiza o contador de pastas
function updateFolderCount() {
  const visibleCount = getVisibleFoldersCount();
  const selectedCount = selectedFolderIds.size;
  
  // Atualiza o contador principal de pastas exibidas
  const countEl = document.getElementById("folderCount");
  countEl.textContent = `Pastas exibidas: ${visibleCount === 0 ? '0' : visibleCount}`;
  
  // Atualiza o contador de pastas selecionadas
  const selectedCountEl = document.getElementById("selectedFoldersCount");
  if (selectedCountEl) {
    selectedCountEl.textContent = `Selecionadas: ${selectedCount}`;
    
    // Adiciona classe visual para destacar quando há pastas selecionadas
    if (selectedCount > 0) {
      selectedCountEl.classList.add("has-selected");
    } else {
      selectedCountEl.classList.remove("has-selected");
    }
  }
}

// Atualiza o contador de favoritos
function updateBookmarkCount() {
  // Contar apenas os favoritos visíveis
  const bookmarkItems = document.querySelectorAll(".bookmark-item");
  const visibleCount = Array.from(bookmarkItems).filter(item => item.style.display !== "none").length;
  
  // Atualizar o contador principal de favoritos exibidos
  // Usar o ID correto que está no HTML atual
  const countEl = document.getElementById("bookmarksDisplayCount");
  
  if (countEl) {
    countEl.textContent = `Favoritos exibidos: ${visibleCount === 0 ? '0' : visibleCount}`;
    
    // Adicionar classe visual para destacar quando há favoritos exibidos
    if (visibleCount > 0) {
      countEl.classList.add("has-bookmarks");
    } else {
      countEl.classList.remove("has-bookmarks");
    }
  }
}



// toggleAllBookmarks agora está em selection.js

/**
 * Função auxiliar para ordenar favoritos com tipo específico.
 * Evita uso de prompts na interface.
 * @param {string} sortBy - Critério de ordenação ('title' ou 'url')
 */
function handleSortBookmarksWithType(sortBy) {
  // Primeira verificação: favoritos selecionados
  if (hasSelectedBookmarks()) {
    sortSelectedBookmarks(sortBy);
    return;
  }
  
  // Segunda verificação: pastas selecionadas
  if (selectedFolderIds.size === 0) {
    showActionFeedback("Selecione uma pasta ou alguns favoritos para ordenar", "info");
    return;
  }

  const folderOptions = Array.from(selectedFolderIds).map(folderId => {
    const checkbox = document.querySelector(`input[type="checkbox"][value="${folderId}"]`);
    const folderName = checkbox ? checkbox.parentElement.textContent.trim() : `Pasta ${folderId}`;
    return { id: folderId, title: folderName };
  });
  
  if (folderOptions.length === 1) {
    // Se há apenas uma pasta selecionada, ordenar diretamente
    sortBookmarksInFolder(folderOptions[0].id, sortBy)
      .then(() => {
        // A mensagem de êxito agora é tratada dentro da função sortBookmarksInFolder
      })
      .catch(error => {
        showActionFeedback(`Erro ao ordenar favoritos: ${error.message}`, 'error');
      });
  } else {
    // Se há múltiplas pastas, mostrar diálogo para escolher
    const folderListText = folderOptions.map((f, i) => `${i}: ${f.title}`).join('\n');
    const folderIndex = prompt(
      `Escolha o número da pasta para ordenar (0-${folderOptions.length - 1}):\n${folderListText}`
    );
    
    if (folderIndex === null) return; // Cancelado pelo usuário
    
    // Verificar se o índice é válido
    const folderIndexNum = parseInt(folderIndex, 10);
    if (isNaN(folderIndexNum) || folderIndexNum < 0 || folderIndexNum >= folderOptions.length) {
      showActionFeedback(`Índice inválido. Escolha um número entre 0 e ${folderOptions.length - 1}.`, 'error');
      return;
    }
    
    const folder = folderOptions[folderIndexNum];
    if (folder) {
      sortBookmarksInFolder(folder.id, sortBy)
        .then(() => {
          // A mensagem de êxito agora é tratada dentro da função sortBookmarksInFolder
        })
        .catch(error => {
          showActionFeedback(`Erro ao ordenar favoritos: ${error.message}`, 'error');
        });
    }
  }
}

/**
 * Função para copiar os títulos dos favoritos selecionados para a área de transferência
 */
function handleCopySelectedTitles() {
  const selectedIds = getSelectedBookmarkIds();

  // Verificar se há favoritos selecionados
  if (selectedIds.length === 0) {
    showActionFeedback("Nenhum favorito selecionado para copiar", "info");
    return;
  }

  // Coletar os títulos dos favoritos selecionados
  const selectedTitles = [];

  // Iterar sobre todos os favoritos selecionados no DOM
  selectedIds.forEach(id => {
    const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
    if (bookmarkItem) {
      const link = bookmarkItem.querySelector('.bookmark-link');
      if (link) {
        selectedTitles.push(link.textContent);
      }
    }
  });

  // Verificar se encontramos algum título
  if (selectedTitles.length === 0) {
    showActionFeedback("Não foi possível encontrar os títulos selecionados", "error");
    return;
  }

  // Criar uma string com os títulos, um por parágrafo
  const textToCopy = selectedTitles.join('\n\n');
  
  // Copiar para a área de transferência
  navigator.clipboard.writeText(textToCopy)
    .then(() => {
      showActionFeedback(`${selectedTitles.length} título(s) copiado(s) para a área de transferência`, "success");
    })
    .catch(err => {
      console.error('Erro ao copiar títulos:', err);
      showActionFeedback("Erro ao copiar títulos", "error");
    });
}

/**
 * Função para copiar as URLs dos favoritos selecionados para a área de transferência
 * em formato HTML (rich text)
 */
function handleCopySelectedUrls() {
  const selectedIds = getSelectedBookmarkIds();

  // Verificar se há favoritos selecionados
  if (selectedIds.length === 0) {
    showActionFeedback("Nenhum favorito selecionado para copiar", "info");
    return;
  }

  // Coletar as URLs e títulos dos favoritos selecionados
  const selectedBookmarks = [];

  // Iterar sobre todos os favoritos selecionados no DOM
  selectedIds.forEach(id => {
    const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
    if (bookmarkItem) {
      const link = bookmarkItem.querySelector('.bookmark-link');
      if (link && link.href) {
        selectedBookmarks.push({
          url: link.href,
          title: link.textContent.trim() || link.href
        });
      }
    }
  });

  // Verificar se encontramos alguma URL
  if (selectedBookmarks.length === 0) {
    showActionFeedback("Não foi possível encontrar as URLs selecionadas", "error");
    return;
  }

  // Criar o conteúdo HTML para copiar
  const htmlContent = selectedBookmarks.map(bookmark => 
    `<a href="${bookmark.url}">${bookmark.title}</a>`
  ).join('<br>\n');
  
  // Para text/plain, usar apenas as URLs
  const textContent = selectedBookmarks.map(bookmark => bookmark.url).join('\n\n');
  
  // Função para copiar conteúdo HTML e texto
  function copyToClipboard(html, text) {
    try {
      const clipboardItem = new ClipboardItem({
        'text/plain': new Blob([text], { type: 'text/plain' }),
        'text/html': new Blob([html], { type: 'text/html' })
      });
      
      navigator.clipboard.write([clipboardItem])
        .then(() => {
          showActionFeedback(`${selectedBookmarks.length} link(s) copiado(s) com sucesso`, "success");
        })
        .catch(err => {
          console.error('Erro ao copiar como HTML:', err);
          // Fallback para método simples se houver erro
          fallbackCopy();
        });
    } catch (err) {
      console.error('ClipboardItem não suportado:', err);
      // Fallback para método simples
      fallbackCopy();
    }
  }
  
  // Método de fallback mais simples
  function fallbackCopy() {
    navigator.clipboard.writeText(textContent)
      .then(() => {
        showActionFeedback(`${selectedBookmarks.length} link(s) copiado(s) (formato simples)`, "info");
      })
      .catch(err => {
        console.error('Erro ao copiar URLs:', err);
        showActionFeedback("Erro ao copiar URLs", "error");
      });
  }
  
  // Tenta copiar como HTML, com fallback para texto simples
  copyToClipboard(htmlContent, textContent);
}

function handleDuplicateSelectedBookmarks() {
  const selectedIds = getSelectedBookmarkIds();

  if (selectedIds.length === 0) {
    showActionFeedback("Nenhum favorito selecionado para duplicar", "info");
    return;
  }

  // Obter todos os itens de favoritos visíveis e selecionados, na ordem em que aparecem na lista
  const allBookmarkItems = Array.from(document.querySelectorAll('.bookmark-item'));
  const selectedItems = allBookmarkItems.filter(item => selectedIds.includes(item.dataset.id));
  if (selectedItems.length === 0) {
    showActionFeedback("Não foi possível encontrar os favoritos selecionados", "error");
    return;
  }

  // Encontrar o último item selecionado na ordem da lista
  const lastSelectedItem = selectedItems[selectedItems.length - 1];
  const parentId = lastSelectedItem.dataset.folder;
  const lastIndex = parseInt(lastSelectedItem.dataset.index, 10);

  // Obter os dados dos favoritos selecionados
  const bookmarksToDuplicate = selectedItems.map(item => {
    const link = item.querySelector('.bookmark-link');
    return {
      title: link ? link.textContent : '',
      url: link ? link.href : '',
    };
  });

  // Duplicar cada favorito, mantendo a ordem, logo após o último selecionado
  let insertIndex = lastIndex + 1;
  const createPromises = bookmarksToDuplicate.map(bookmark => {
    return new Promise(resolve => {
      chrome.bookmarks.create({
        parentId: parentId,
        title: bookmark.title,
        url: bookmark.url,
        index: insertIndex++
      }, resolve);
    });
  });

  Promise.all(createPromises).then(() => {
    // Forçar re-renderização completa para refletir a nova ordem imediatamente
    if (typeof window.reloadSelectedFolders === 'function') {
      window.reloadSelectedFolders();
    } else if (typeof reloadSelectedFolders === 'function') {
      reloadSelectedFolders();
    } else {
      // Fallback: limpar e recarregar bookmarks das pastas selecionadas
      bookmarksContainer.innerHTML = '';
      selectedFolderIds.forEach(folderId => {
        chrome.bookmarks.getChildren(folderId, (children) => {
          if (!chrome.runtime.lastError) {
            const items = children.filter(c => c.url);
            renderBookmarks(items, bookmarksContainer, false, folderId, false);
          }
        });
      });
    }
    showActionFeedback(`${bookmarksToDuplicate.length} favorito(s) duplicado(s)`, "success");
  });
}