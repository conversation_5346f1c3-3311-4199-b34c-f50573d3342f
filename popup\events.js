// events.js

/**
 * Atualiza o seletor de pastas.
 */
function populateFolderCheckboxes(folders, containerEl) {
  containerEl.innerHTML = ''; // Limpa antes

  folders.forEach((folder) => {
    if (!folder.url) { // Verifica se é uma pasta
      const label = document.createElement("label");
      label.className = "folder-option";

      // Habilitar drag and drop para pastas
      label.setAttribute("draggable", "true");
  
      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.value = folder.id;
      checkbox.className = "folder-checkbox";
      
      // Impedir que os checkboxes respondam diretamente aos cliques
      checkbox.style.pointerEvents = "none";
  
      checkbox.addEventListener("change", () => {
        const folderId = checkbox.value;
        
        // Aplicar alterações visuais imediatamente
        if (checkbox.checked) {
          selectedFolderIds.add(folderId);
          label.classList.add('selected');
          
          // Atualizar diretamente o contador de pastas selecionadas
          const selectedCountEl = document.getElementById("selectedFoldersCount");
          if (selectedCountEl) {
            selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
            selectedCountEl.classList.add("has-selected");
          }
          
          // Verificar se a renderização não está bloqueada antes de processar favoritos
          if (!blockRendering) {
            // Obter favoritos desta pasta de forma assíncrona
            setTimeout(() => {
              chrome.bookmarks.getChildren(folderId, (children) => {
                if (chrome.runtime.lastError) {
                  console.error("Erro ao obter filhos:", chrome.runtime.lastError);
                  return;
                }
                
                // Filtrar para obter apenas favoritos (itens com URL)
                const items = children.filter(c => c.url);
                
                // Armazenar os IDs para referência
                folderBookmarkMap.set(folderId, items.map(b => b.id));
                
                // Forçar a recriação dos elementos (não usar cache para favicons)
                // Remover elementos existentes para esta pasta para evitar duplicação
                const existingItems = bookmarksContainer.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`);
                existingItems.forEach(el => el.remove());
                
                // Limpar e recriar os elementos do cache para esta pasta
                items.forEach(item => {
                  // Se o item estiver no cache, força a recriação do favicon
                  if (bookmarkElementCache.has(item.id)) {
                    const cachedElement = bookmarkElementCache.get(item.id);
                    const favicon = cachedElement.querySelector('.favicon');
                    if (favicon) {
                      // Redefine o favicon para forçar o carregamento
                      favicon.src = getFaviconUrl(item.url);
                    }
                  }
                });
                
                // Renderizar com o cache atualizado
                renderBookmarks(items, bookmarksContainer, false, folderId, false);
              });
            }, 0);
          } else {
            console.log(`Renderização bloqueada: ignorando carregamento de favoritos para pasta ${folderId}`);
          }
          
        } else {
          selectedFolderIds.delete(folderId);
          label.classList.remove('selected');
          
          // Atualizar diretamente o contador de pastas selecionadas
          const selectedCountEl = document.getElementById("selectedFoldersCount");
          if (selectedCountEl) {
            selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
            if (selectedFolderIds.size === 0) {
              selectedCountEl.classList.remove("has-selected");
            }
          }
          
          // Remover apenas os elementos visíveis, mas manter o cache
          const idsToRemove = folderBookmarkMap.get(folderId) || [];
          idsToRemove.forEach(id => {
            const el = bookmarksContainer.querySelector(`[data-id="${id}"]`);
            if (el) {
              // Remover apenas da visualização, não do cache
              el.remove();
            }
          });
          
          folderBookmarkMap.delete(folderId);
          updateBookmarkCount();
        }
        
        // Atualizar a contagem de pastas imediatamente
        updateFolderCount();
      });
      
      // Criar o contêiner do título com ícone
      const titleContainer = document.createElement("div");
      titleContainer.className = "folder-title-container";
      
      // Criar o ícone de pasta como um elemento SVG
      const svgNS = "http://www.w3.org/2000/svg";
      const folderIcon = document.createElementNS(svgNS, "svg");
      folderIcon.setAttribute("width", "16");
      folderIcon.setAttribute("height", "16");
      folderIcon.setAttribute("viewBox", "0 0 20 20");
      folderIcon.setAttribute("class", "folder-icon");
      
      // Caminho de fundo para o corpo da pasta
      const folderBg = document.createElementNS(svgNS, "path");
      folderBg.setAttribute("d", "M2 5.5C2 4.12 3.12 3 4.5 3h2.83c.27 0 .53.07.76.21l.14.09 1.6 1.2h5.83c1.38 0 2.5 1.12 2.5 2.5v7.5c0 1.38-1.12 2.5-2.5 2.5h-11C3.12 17 2 15.88 2 14.5v-9z");
      folderBg.setAttribute("fill", "#666666");
      folderBg.setAttribute("opacity", "0.2");
      
      // Caminho de contorno para a pasta
      const path = document.createElementNS(svgNS, "path");
      path.setAttribute("d", "M7.17 3c.27 0 .53.07.76.21l.14.09 1.6 1.2h5.83a2.5 2.5 0 012.48 2.17l.01.17L18 7v7.5a2.5 2.5 0 01-2.34 2.5H4.5A2.5 2.5 0 012 14.66V5.5A2.5 2.5 0 014.34 3h2.83zm.99 4.03a1.5 1.5 0 01-.94.46l-.15.01H3v7c0 .78.6 1.42 1.36 1.5H15.5c.78 0 1.42-.6 1.5-1.36V7c0-.78-.6-1.42-1.36-1.5H9.62L8.16 7.03zM7.16 4H4.5c-.78 0-1.42.6-1.5 1.36V6.5h4.07a.5.5 0 00.3-.1l.06-.06L8.7 5.02 7.47 4.1a.5.5 0 00-.22-.1h-.08z");
      path.setAttribute("fill", "#666666");
      path.setAttribute("fill-rule", "nonzero");
      
      folderIcon.appendChild(folderBg);
      folderIcon.appendChild(path);
      titleContainer.appendChild(folderIcon);
  
      const titleSpan = document.createElement("span");
      titleSpan.className = "folder-title";
      titleSpan.textContent = folder.title || "(Sem nome)";
      
      titleContainer.appendChild(titleSpan);
  
      label.appendChild(checkbox);
      label.appendChild(titleContainer);
      containerEl.appendChild(label);
      
      // Recursivamente adiciona subpastas
      if (folder.children) {
        const subContainer = document.createElement("div");
        subContainer.className = "folder-nest";
        containerEl.appendChild(subContainer);
        populateFolderCheckboxes(folder.children, subContainer);
      }
    }
  });
}

/**
 * Atualiza seletivamente a pasta com base nos novos índices.
 */
function updateFolderSelectively(folderId) {
  chrome.bookmarks.getChildren(folderId, (bookmarks) => {
    if (chrome.runtime.lastError) {
      console.error('Erro ao atualizar pasta:', chrome.runtime.lastError);
      return;
    }

    // Filtrar apenas favoritos (com URL)
    const urlBookmarks = bookmarks.filter(bm => bm.url);

    // Obter elementos atuais e criar mapa de ID -> índice
    const bookmarkElements = Array.from(bookmarksContainer.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`));
    const indexMap = new Map();
    urlBookmarks.forEach((bookmark, index) => {
      indexMap.set(bookmark.id, index);
    });

    // Ordenar elementos com base nos novos índices
    const sortedElements = bookmarkElements
      .filter(el => el.dataset.id && indexMap.has(el.dataset.id))
      .sort((a, b) => {
        return indexMap.get(a.dataset.id) - indexMap.get(a.dataset.id);
      });

    // Remove os elementos antigos
    bookmarkElements.forEach(el => el.remove());

    // Adiciona os elementos ordenados
    const fragment = document.createDocumentFragment();
    sortedElements.forEach(element => {
      // Atualizar o índice do elemento
      const id = element.dataset.id;
      if (id && indexMap.has(id)) {
        element.dataset.index = indexMap.get(id);
      }
      fragment.appendChild(element);
    });
    
    bookmarksContainer.appendChild(fragment);

    // Atualizar o cache
    folderBookmarkMap.set(folderId, urlBookmarks.map(b => b.id));
    
    // Verificar por novos bookmarks não renderizados
    const existingIds = new Set(sortedElements.map(el => el.dataset.id));
    const newBookmarks = urlBookmarks.filter(bm => !existingIds.has(bm.id));
    
    if (newBookmarks.length > 0) {
      renderBookmarks(newBookmarks, bookmarksContainer, true, folderId);
    }

    updateBookmarkCount();
  });
}

/**
 * Recarrega todos os favoritos das pastas selecionadas.
 */
function reloadSelectedFolders() {
  bookmarksContainer.innerHTML = '';
  
  const promises = Array.from(selectedFolderIds).map(folderId => 
    getBookmarksPromise(folderId).then(children => {
      const items = children.filter(c => c.url);
      folderBookmarkMap.set(folderId, items.map(b => b.id));
      return { folderId, items };
    })
  );
  
  Promise.all(promises).then(results => {
    results.forEach(({ folderId, items }) => {
      // Always clear previous bookmarks for this folder before rendering
      // Remove all .bookmark-item elements for this folder
      const toRemove = Array.from(bookmarksContainer.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`));
      toRemove.forEach(el => el.remove());
      // Render bookmarks in the correct order, not appending
      renderBookmarks(items, bookmarksContainer, false, folderId);
    });
  });
}

/**
 * Função para mesclar favoritos de várias pastas em uma pasta de destino
 * @param {Array<string>} sourceFolderIds - IDs das pastas de origem
 * @param {string} targetFolderId - ID da pasta de destino
 * @returns {Promise<{success: boolean, message: string, count: number}>} - Resultado da operação
 */
function mergeFolders(sourceFolderIds, targetFolderId) {
  return new Promise(async (resolve) => {
    // Verificar se há pelo menos uma pasta de origem e uma de destino
    if (!sourceFolderIds || sourceFolderIds.length === 0 || !targetFolderId) {
      resolve({
        success: false,
        message: "Selecione pelo menos uma pasta de origem e uma pasta de destino",
        count: 0
      });
      return;
  }

    // Verificar se a pasta de destino está na lista de origens
  if (sourceFolderIds.includes(targetFolderId)) {
      resolve({
        success: false,
        message: "A pasta de destino não pode ser uma das pastas de origem",
        count: 0
      });
      return;
    }
    
    try {
      // Obter todos os favoritos das pastas de origem
      const allBookmarks = [];
      let processedFolders = 0;
      
      // Processar cada pasta de origem
      for (const folderId of sourceFolderIds) {
        try {
          const bookmarks = await getBookmarksPromise(folderId);
          // Filtrar apenas favoritos (com URL)
          const validBookmarks = bookmarks.filter(bm => bm.url);
          allBookmarks.push(...validBookmarks);
          processedFolders++;
        } catch (error) {
          console.error(`Erro ao obter favoritos da pasta ${folderId}:`, error);
        }
      }
      
      // Se não conseguimos processar nenhuma pasta, retornar erro
      if (processedFolders === 0) {
        resolve({
          success: false,
          message: "Não foi possível acessar nenhuma das pastas selecionadas",
          count: 0
        });
        return;
      }
      
      // Se não há favoritos para mesclar, retornar erro
      if (allBookmarks.length === 0) {
        resolve({
          success: false,
          message: "Não há favoritos para mesclar nas pastas selecionadas",
          count: 0
        });
        return;
      }
      
      // Contador de favoritos mesclados com sucesso
      let successCount = 0;
      
      // Processar favoritos em lotes para evitar sobrecarga
      const batchSize = 20;
      
      // Função para processar um lote de favoritos
      const processBatch = async (bookmarks, startIndex) => {
        if (startIndex >= bookmarks.length) {
          // Todos os lotes foram processados
          resolve({
            success: true,
            message: `${successCount} favoritos mesclados com sucesso`,
            count: successCount
          });
          return;
        }
        
        // Calcular o índice final do lote atual
        const endIndex = Math.min(startIndex + batchSize, bookmarks.length);
        const currentBatch = bookmarks.slice(startIndex, endIndex);
        
        // Processar cada favorito do lote atual
        const promises = currentBatch.map(bookmark => {
          return new Promise(resolveBookmark => {
            chrome.bookmarks.create({
            parentId: targetFolderId,
              title: bookmark.title,
              url: bookmark.url
            }, (result) => {
              if (chrome.runtime.lastError) {
                console.error("Erro ao criar favorito:", chrome.runtime.lastError);
                resolveBookmark(false);
              } else {
                successCount++;
                resolveBookmark(true);
              }
            });
          });
        });
        
        // Aguardar o processamento de todos os favoritos do lote atual
        await Promise.all(promises);
        
        // Processar o próximo lote
        setTimeout(() => {
          processBatch(bookmarks, endIndex);
        }, 10);
      };
      
      // Iniciar o processamento do primeiro lote
      processBatch(allBookmarks, 0);
      
    } catch (error) {
      console.error("Erro ao mesclar pastas:", error);
      resolve({
        success: false,
        message: "Erro ao mesclar pastas: " + error.message,
        count: 0
      });
      }
    });
}

/**
 * Manipulador para a ação de mesclar pastas
 */
function handleMergeFolders() {
  // Verificar se há pastas selecionadas
  if (selectedFolderIds.size === 0) {
    showActionFeedback("Selecione pelo menos uma pasta de origem", "error");
    return;
  }
  
  // Verificar configuração de confirmação
  // (extensionSettings é definido no popup.js)
  if (window.extensionSettings && window.extensionSettings.confirmMerge) {
    const confirmMessage = `Deseja mesclar os favoritos das ${selectedFolderIds.size} pasta(s) selecionada(s)?`;
    if (!confirm(confirmMessage)) return;
  }
  
  // Converter o Set para Array para manipulação
  const selectedFolders = Array.from(selectedFolderIds);
  
  // Criar o diálogo de seleção de pasta de destino
  const dialog = document.createElement("div");
  dialog.className = "merge-dialog";
  dialog.innerHTML = `
    <div class="merge-dialog-content">
      <h2>Mesclar Favoritos</h2>
      <p>Selecione a pasta de destino:</p>
      <div class="target-folder-select">
        <select id="targetFolderSelect">
          <option value="">Selecione uma pasta...</option>
        </select>
      </div>
      <div class="merge-options">
        <label>
          <input type="checkbox" id="skipDuplicates" checked>
          Ignorar favoritos duplicados
        </label>
      </div>
      <div class="merge-dialog-buttons">
        <button id="cancelMergeBtn" class="secondary-btn">Cancelar</button>
        <button id="confirmMergeBtn" class="primary-btn">Mesclar</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(dialog);
  
  // Preencher o select com todas as pastas disponíveis
  const targetFolderSelect = document.getElementById("targetFolderSelect");
  
  // Obter todas as pastas
  chrome.bookmarks.getTree(async (tree) => {
    try {
      // Função para adicionar opções de pasta recursivamente
      function addFolderOptions(folders, level = 0) {
        folders.forEach(folder => {
          // Verificar se é uma pasta (não tem URL)
          if (!folder.url) {
            // Não adicionar pastas que já estão selecionadas como origem
            if (!selectedFolders.includes(folder.id)) {
              const option = document.createElement("option");
              option.value = folder.id;
              option.textContent = "—".repeat(level) + " " + (folder.title || "(Sem nome)");
              targetFolderSelect.appendChild(option);
            }
            
            // Adicionar subpastas recursivamente
            if (folder.children) {
              addFolderOptions(folder.children, level + 1);
            }
          }
        });
      }
      
      // Adicionar opções de pasta
      addFolderOptions(tree[0].children);
      
      // Configurar eventos dos botões
      document.getElementById("cancelMergeBtn").addEventListener("click", () => {
        document.body.removeChild(dialog);
      });
      
      document.getElementById("confirmMergeBtn").addEventListener("click", async () => {
        const targetFolderId = targetFolderSelect.value;
        
        // Verificar se uma pasta de destino foi selecionada
        if (!targetFolderId) {
          showActionFeedback("Selecione uma pasta de destino", "error");
          return;
        }
        
        // Desabilitar botões durante o processo
        const confirmBtn = document.getElementById("confirmMergeBtn");
        const cancelBtn = document.getElementById("cancelMergeBtn");
        confirmBtn.disabled = true;
        cancelBtn.disabled = true;
        confirmBtn.textContent = "Mesclando...";
        
        // Verificar se deve ignorar duplicados
        const skipDuplicates = document.getElementById("skipDuplicates").checked;
        
        try {
          // Mostrar feedback de processamento
          showActionFeedback("Mesclando favoritos...", "info");
          
          // Executar a mesclagem
          const result = await mergeFolders(selectedFolders, targetFolderId);
          
          // Remover o diálogo
          document.body.removeChild(dialog);
          
          // Mostrar feedback do resultado
          if (result.success) {
            showActionFeedback(result.message, "success");
            
            // Recarregar a pasta de destino se estiver selecionada
            if (selectedFolderIds.has(targetFolderId)) {
              reloadSelectedFolders([targetFolderId]);
            }
          } else {
            showActionFeedback(result.message, "error");
          }
        } catch (error) {
          console.error("Erro ao mesclar pastas:", error);
          showActionFeedback("Erro ao mesclar pastas", "error");
          
          // Restaurar os botões
          confirmBtn.disabled = false;
          cancelBtn.disabled = false;
          confirmBtn.textContent = "Mesclar";
        }
      });
    } catch (error) {
      console.error("Erro ao preparar diálogo de mesclagem:", error);
      showActionFeedback("Erro ao preparar diálogo de mesclagem", "error");
      document.body.removeChild(dialog);
  }
  });
}

/**
 * Ordena os favoritos na pasta selecionada de acordo com um critério.
 * @param {string} folderId - ID da pasta para ordenar
 * @param {string} sortBy - Critério de ordenação ('title', 'url', 'domain', 'dateAdded', 'dateModified')
 * @returns {Promise} Promise resolvida após a ordenação
 */
function sortBookmarksInFolder(folderId, sortBy = 'title') {
  return getBookmarksPromise(folderId)
    .then(bookmarks => {
      const bookmarksToSort = bookmarks.filter(b => b.url);
      
      // Função para comparar favoritos com base no critério escolhido
      const compareFn = (a, b) => {
        switch (sortBy) {
          case 'title':
            return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
          
          case 'url':
            return a.url.toLowerCase().localeCompare(b.url.toLowerCase());
          
          case 'domain':
            const domainA = extractDomain(a.url);
            const domainB = extractDomain(b.url);
            return domainA.localeCompare(domainB);
          
          case 'dateAdded':
            // Ordena do mais recente para o mais antigo
            return b.dateAdded - a.dateAdded;
          
          case 'dateModified':
            // Ordena do mais recente para o mais antigo
            return b.dateGroupModified - a.dateGroupModified;
          
          default:
            return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
        }
      };
      
      const sortedBookmarks = [...bookmarksToSort].sort(compareFn);
      
      // Prepara operações de movimentação
      const moveOps = sortedBookmarks.map((bookmark, index) => 
        new Promise(resolve => {
          chrome.bookmarks.move(bookmark.id, { 
            parentId: folderId, 
            index: index 
          }, resolve);
        })
      );
      
      return Promise.all(moveOps);
    })
    .then(() => {
      updateFolderSelectively(folderId);
      
      // Mensagens amigáveis para o usuário exatamente como aparecem no menu
      let mensagem;
      switch (sortBy) {
        case 'title':
          mensagem = 'Título';
          break;
        case 'url':
          mensagem = 'URL';
          break;
        case 'domain':
          mensagem = 'Domínio';
          break;
        case 'dateAdded':
          mensagem = 'Data de Criação';
          break;
        case 'dateModified':
          mensagem = 'Data de Modificação';
          break;
        default:
          mensagem = sortBy;
      }
      
      // Se folderId estiver definido, adicione o nome da pasta na mensagem
      const folderName = getFolderName(folderId);
      if (folderName) {
        showActionFeedback(`Favoritos em "${folderName}" ordenados por ${mensagem}`);
      } else {
        showActionFeedback(`Favoritos ordenados por ${mensagem}`);
      }
    });
}

/**
 * Ordena os favoritos selecionados dentro de suas pastas.
 * @param {string} sortBy - Critério de ordenação ('title', 'url', 'domain', 'dateAdded', 'dateModified')
 */
function sortSelectedBookmarks(sortBy = 'title') {
  const selectedIds = getSelectedBookmarkIds();

  if (selectedIds.length === 0) {
    alert("Nenhum favorito selecionado para classificar");
    return;
  }

  // Agrupar favoritos selecionados por pasta
  const folderGroups = new Map();

  selectedIds.forEach(id => {
    const element = bookmarksContainer.querySelector(`[data-id="${id}"]`);
    if (element) {
      const folderId = element.dataset.folder;
      if (!folderGroups.has(folderId)) {
        folderGroups.set(folderId, []);
      }
      folderGroups.get(folderId).push(id);
    }
  });

  // Obter informações dos favoritos
  const promises = [];
  folderGroups.forEach((bookmarkIds, folderId) => {
    const getPromises = bookmarkIds.map(id => 
      new Promise(resolve => {
        chrome.bookmarks.get(id, bookmarks => {
          if (bookmarks && bookmarks.length > 0) {
            resolve(bookmarks[0]);
          } else {
            resolve(null);
          }
        });
      })
    );
    
    promises.push(
      Promise.all(getPromises).then(bookmarks => {
        // Filtrar nulos
        const validBookmarks = bookmarks.filter(b => b !== null);
        
        // Função para comparar favoritos com base no critério escolhido
        const compareFn = (a, b) => {
          switch (sortBy) {
            case 'title':
              return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
            
            case 'url':
              return a.url.toLowerCase().localeCompare(b.url.toLowerCase());
            
            case 'domain':
              const domainA = extractDomain(a.url);
              const domainB = extractDomain(b.url);
              return domainA.localeCompare(domainB);
            
            case 'dateAdded':
              // Ordena do mais recente para o mais antigo
              return b.dateAdded - a.dateAdded;
            
            case 'dateModified':
              // Ordena do mais recente para o mais antigo
              return b.dateGroupModified - a.dateGroupModified;
            
            default:
              return a.title.toLowerCase().localeCompare(b.title.toLowerCase());
          }
        };
        
        // Ordenar os favoritos
        const sortedBookmarks = validBookmarks.sort(compareFn);
        
        // Mover os favoritos para a posição correta
        const movePromises = sortedBookmarks.map((bookmark, index) => 
          new Promise(resolve => {
            chrome.bookmarks.move(bookmark.id, { 
              index: index 
            }, resolve);
          })
        );
        
        return Promise.all(movePromises).then(() => folderId);
      })
    );
  });
  
  Promise.all(promises).then(folderIds => {
    // Atualizar UI para cada pasta afetada
    // folderIds.forEach(folderId => {
    //   if (folderId) updateFolderSelectively(folderId);
    // });
    reloadSelectedFolders();
    // Mensagens amigáveis para o usuário exatamente como aparecem no menu
    let mensagem;
    switch (sortBy) {
      case 'title':
        mensagem = 'Título';
        break;
      case 'url':
        mensagem = 'URL';
        break;
      case 'domain':
        mensagem = 'Domínio';
        break;
      case 'dateAdded':
        mensagem = 'Data de Criação';
        break;
      case 'dateModified':
        mensagem = 'Data de Modificação';
        break;
      default:
        mensagem = sortBy;
    }
    
    showActionFeedback(`${selectedBookmarkIds.size} favoritos classificados por ${mensagem}`);
  });
}

/**
 * Ordena os favoritos na pasta selecionada.
 * CORRIGIDO: Verifica corretamente os retornos nulos dos prompts
 */
function handleSortBookmarks() {
  // Primeira verificação: favoritos selecionados
  if (selectedBookmarkIds.size > 0) {
    const sortType = prompt("Ordenar por: (1) Título ou (2) URL?", "1");
    if (sortType === null) return; // Cancelado pelo usuário
    
    const sortBy = sortType === "2" ? "url" : "title";
    sortSelectedBookmarks(sortBy);
    return;
  }
  
  // Segunda verificação: pastas selecionadas
  if (selectedFolderIds.size === 0) {
    alert("Selecione uma pasta ou alguns favoritos para ordenar");
    return;
  }

  const folderOptions = Array.from(selectedFolderIds).map(folderId => {
    const checkbox = document.querySelector(`input[type="checkbox"][value="${folderId}"]`);
    const folderName = checkbox ? checkbox.parentElement.textContent.trim() : `Pasta ${folderId}`;
    return { id: folderId, title: folderName };
  });
  
  if (folderOptions.length === 1) {
    // Se há apenas uma pasta selecionada, pergunte o critério e ordene
    const sortType = prompt("Ordenar por: (1) Título ou (2) URL?", "1");
    if (sortType === null) return; // Cancelado pelo usuário
    
    const sortBy = sortType === "2" ? "url" : "title";
    
    sortBookmarksInFolder(folderOptions[0].id, sortBy)
      .then(() => {
        let mensagem;
        switch (sortBy) {
          case 'title':
            mensagem = 'Título';
            break;
          case 'url':
            mensagem = 'URL';
            break;
          default:
            mensagem = sortBy;
        }
        showActionFeedback(`Favoritos em "${folderOptions[0].title}" ordenados por ${mensagem}`);
      })
      .catch(error => {
        showActionFeedback(`Erro ao ordenar favoritos: ${error.message}`, 'error');
      });
      
  } else {
    // Se há múltiplas pastas, pergunte qual ordenar
    const folderIndex = prompt(
      `Escolha o número da pasta para ordenar (0-${folderOptions.length - 1}):\n` +
      folderOptions.map((f, i) => `${i}: ${f.title}`).join('\n')
    );
    
    if (folderIndex === null) return; // Cancelado pelo usuário
    
    // Verificar se o índice é válido
    const folderIndexNum = parseInt(folderIndex, 10);
    if (isNaN(folderIndexNum) || folderIndexNum < 0 || folderIndexNum >= folderOptions.length) {
      alert(`Índice inválido. Por favor, escolha um número entre 0 e ${folderOptions.length - 1}.`);
      return;
    }
    
    const folder = folderOptions[folderIndexNum];
    if (folder) {
      const sortType = prompt("Ordenar por: (1) Título ou (2) URL?", "1");
      if (sortType === null) return; // Cancelado pelo usuário
      
      const sortBy = sortType === "2" ? "url" : "title";
      
      sortBookmarksInFolder(folder.id, sortBy)
        .then(() => {
          let mensagem;
          switch (sortBy) {
            case 'title':
              mensagem = 'Título';
              break;
            case 'url':
              mensagem = 'URL';
              break;
            default:
              mensagem = sortBy;
          }
          showActionFeedback(`Favoritos em "${folder.title}" ordenados por ${mensagem}`);
        })
        .catch(error => {
          showActionFeedback(`Erro ao ordenar favoritos: ${error.message}`, 'error');
        });
    }
  }
}

/**
 * Marca/desmarca todas as pastas visíveis após filtragem.
 * @param {boolean} checked - Se devem ser marcadas ou desmarcadas
 */
function toggleAllFolders(checked) {
  // Obter todos os checkboxes de pasta que estão visíveis
  const visibleFolderOptions = Array.from(document.querySelectorAll(".folder-option"))
    .filter(option => option.style.display !== "none");
  
  if (visibleFolderOptions.length === 0) {
    showActionFeedback("Nenhuma pasta visível para selecionar", "info");
    return;
  }
  
  // Otimização: bloquear a renderização durante a operação em lote
  blockRendering = true;

  visibleFolderOptions.forEach(option => {
    const checkbox = option.querySelector("input[type='checkbox']");
    if (checkbox && checkbox.checked !== checked) {
      checkbox.checked = checked;
      // Disparar o evento 'change' para que a lógica de seleção/deseleção seja executada
      checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    }
    // Adiciona ou remove a classe 'selected' do elemento pai (label)
    if (checked) {
      option.classList.add('selected');
    } else {
      option.classList.remove('selected');
    }
  });
  
  // Desbloquear a renderização após a conclusão
  blockRendering = false;
  
  // Atualizar o contador de pastas selecionadas
  const selectedCountEl = document.getElementById("selectedFoldersCount");
  if (selectedCountEl) {
    selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
    if (selectedFolderIds.size > 0) {
      selectedCountEl.classList.add("has-selected");
    } else {
      selectedCountEl.classList.remove("has-selected");
    }
  }
  
  // Atualizar a contagem de pastas
  updateFolderCount();
  updateBookmarkCount();
}

// Gerenciamento do tema (claro/escuro)
document.addEventListener('DOMContentLoaded', () => {
  const themeToggleBtn = document.getElementById('themeToggleBtn');
  if (!themeToggleBtn) return;

  themeToggleBtn.addEventListener('click', () => {
    // Alternar o estado do tema na configuração
    const currentTheme = extensionSettings.theme;
    let newTheme;
    
    // Lógica para alternar: se for 'light' ou 'system' que resultou em light, vai para 'dark', e vice-versa
    const isCurrentlyDark = document.body.classList.contains('dark-theme');
    if (isCurrentlyDark) {
        newTheme = 'light';
    } else {
        newTheme = 'dark';
    }

    extensionSettings.theme = newTheme;

    // Salvar a nova configuração
    chrome.storage.sync.set({ settings: extensionSettings }, () => {
        console.log('Tema salvo:', newTheme);
        // Reaplicar todos os estilos e classes
        applyThemeFromSettings();
    });
    
    // Mostrar feedback
    showActionFeedback(`Tema ${newTheme === 'dark' ? 'escuro' : 'claro'} ativado`, 'info');
  });
});

// Função para mostrar feedback de ações
function showActionFeedback(message, type = 'info') {
  // Verificar se já existe um elemento de feedback
  let feedback = document.querySelector('.action-feedback');
  
  // Se não existir, criar um novo
  if (!feedback) {
    feedback = document.createElement('div');
    feedback.className = `action-feedback ${type}`;
    document.body.appendChild(feedback);
  } else {
    // Atualizar a classe para o tipo correto
    feedback.className = `action-feedback ${type}`;
  }
  
  // Definir a mensagem
  feedback.textContent = message;
  
  // Exibir o feedback
  feedback.style.opacity = '1';
  feedback.style.bottom = 'calc(var(--footer-height) + 18px)';
  
  // Ocultar após 2 segundos
  setTimeout(() => {
    feedback.style.opacity = '0';
    
    // Remover o elemento após a transição
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 300);
  }, 2000);
}

// Executar a configuração dos botões de limpar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', () => {
  // Chamar a função para configurar os botões de limpar, que agora está em utils.js
  setupClearSearchButtons();
  
  // Verificar e atualizar os botões quando os campos de pesquisa mudarem
  const folderSearch = document.getElementById('folderSearch');
  const bookmarkSearch = document.getElementById('bookmarkSearch');
  
  if (folderSearch) {
    folderSearch.addEventListener('input', () => {
      const clearButton = folderSearch.closest('.search-container').querySelector('.clear-search-btn');
      if (clearButton) {
        clearButton.style.display = folderSearch.value ? 'block' : 'none';
      }
    });
  }
  
  if (bookmarkSearch) {
    bookmarkSearch.addEventListener('input', () => {
      const clearButton = bookmarkSearch.closest('.search-container').querySelector('.clear-search-btn');
      if (clearButton) {
        clearButton.style.display = bookmarkSearch.value ? 'block' : 'none';
      }
    });
  }
});

/**
 * Configura a seleção de pastas com clique direito do mouse
 */
function configureFolderRightClickSelection() {
  const folderCheckboxesContainer = document.getElementById("folderCheckboxes");

  folderCheckboxesContainer.addEventListener("contextmenu", (e) => {
    // Encontrar o elemento de pasta mais próximo
    const folderOption = e.target.closest(".folder-option");
    if (!folderOption) return;

    // Impedir o menu de contexto padrão
    e.preventDefault();

    // Encontrar o checkbox dentro do folderOption
    const checkbox = folderOption.querySelector("input[type='checkbox']");
    if (!checkbox) return;

    // Inverter o estado do checkbox
    checkbox.checked = !checkbox.checked;

    // Disparar o evento change para que os manipuladores sejam acionados
    const changeEvent = new Event("change", { bubbles: true });
    checkbox.dispatchEvent(changeEvent);

    // Atualizar o último checkbox clicado para suporte ao Shift
    lastClickedFolderCheckbox = checkbox;
  });
}

// configureBookmarkRightClickSelection agora está em selection.js

/**
 * Configura a seleção de pastas usando a tecla Shift
 */
function setupFolderShiftSelection() {
  // Configurar seleção com Shift para pastas
  const folderCheckboxesContainer = document.getElementById("folderCheckboxes");
  
  folderCheckboxesContainer.addEventListener("click", (e) => {
    // Verificar se é um checkbox de pasta
    if (e.target.classList.contains("folder-checkbox")) {
      const currentCheckbox = e.target;
      
      // Se o Shift está pressionado e há um checkbox anterior clicado
      if (e.shiftKey && lastClickedFolderCheckbox) {
        // Obter todos os checkboxes visíveis
        const checkboxes = Array.from(document.querySelectorAll(".folder-checkbox"))
          .filter(cb => {
            // Verificar se o checkbox está visível (seu container não está escondido)
            const container = cb.closest(".folder-option");
            return container && window.getComputedStyle(container).display !== "none";
          });
        
        // Encontrar índices do checkbox atual e do último clicado
        const currentIndex = checkboxes.indexOf(currentCheckbox);
        const lastIndex = checkboxes.indexOf(lastClickedFolderCheckbox);
        
        // Se ambos foram encontrados
        if (currentIndex !== -1 && lastIndex !== -1) {
          // Determinar intervalo
          const startIndex = Math.min(currentIndex, lastIndex);
          const endIndex = Math.max(currentIndex, lastIndex);
          
          // Selecionar todos os checkboxes no intervalo
          const targetState = currentCheckbox.checked;
          
          // Bloquear renderização temporariamente
          window.blockRendering = true;
          
          for (let i = startIndex; i <= endIndex; i++) {
            const checkbox = checkboxes[i];
            // Só alterar se for diferente do estado alvo
            if (checkbox.checked !== targetState) {
              checkbox.checked = targetState;
              
              // Disparar evento change para atualizar a seleção
              const changeEvent = new Event("change", { bubbles: true });
              checkbox.dispatchEvent(changeEvent);
            }
          }
          
          // Liberar renderização após um pequeno atraso
          setTimeout(() => {
            window.blockRendering = false;
            updateFolderCount();
            updateBookmarkCount();
          }, 100);
        }
      }
      
      // Atualizar o último checkbox clicado
      lastClickedFolderCheckbox = currentCheckbox;
    }
  });
  
  // Configuração de seleção de bookmarks movida para sortable.js para evitar conflitos
}