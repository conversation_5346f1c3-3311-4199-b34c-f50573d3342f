// render.js

const bookmarkElementCache = new Map();
// Declarar a variável renderInProgress no escopo global
let renderInProgress = false;
// Flag para indicar que nenhuma renderização deve ocorrer
let blockRendering = false;
// Cache de favicons para evitar recarregamentos
const faviconCache = new Map();
// Contador para carregamento em lotes
let faviconBatchCounter = 0;
// Limite de tempo para manter favicons em cache (24 horas em ms)
const FAVICON_CACHE_EXPIRY = 24 * 60 * 60 * 1000;
// Tamanho máximo do cache de favicons
const MAX_FAVICON_CACHE_SIZE = 500;

// Definir o ícone de estrela SVG para uso em toda a aplicação
const starSvgIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="%23FFD700" stroke="%23E5B122" stroke-width="1.5">
  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
</svg>`;
const starIconDataUrl = `data:image/svg+xml;utf8,${starSvgIcon}`;

// Função para limpar o cache de favicons quando ele ficar muito grande
function pruneFaviconCache() {
  if (faviconCache.size <= MAX_FAVICON_CACHE_SIZE) return;
  
  console.log(`Limpando cache de favicons (tamanho: ${faviconCache.size})`);
  
  // Converter para array para poder ordenar
  const entries = Array.from(faviconCache.entries());
  
  // Ordenar por timestamp (mais antigos primeiro)
  entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
  
  // Remover os 30% mais antigos
  const removeCount = Math.floor(faviconCache.size * 0.3);
  entries.slice(0, removeCount).forEach(([key]) => {
    faviconCache.delete(key);
  });
  
  console.log(`Cache de favicons reduzido para ${faviconCache.size} itens`);
}

// Função para detectar o navegador e usar o protocolo favicon correto
function getFaviconUrl(url) {
  try {
    if (!url) return starIconDataUrl;

    // Verificar se já temos o favicon em cache e se não expirou
    if (faviconCache.has(url)) {
      const cachedItem = faviconCache.get(url);
      const now = Date.now();
      
      // Se o item não expirou, retorná-lo
      if (now - cachedItem.timestamp < FAVICON_CACHE_EXPIRY) {
        return cachedItem.url;
      }
      // Se expirou, remover do cache
      faviconCache.delete(url);
    }

    // Verificar e fornecer ícones específicos para URLs internas do navegador
    if (url.startsWith('chrome://') || url.startsWith('edge://') || 
        url.startsWith('about:') || url.startsWith('browser://')) {
      const iconUrl = starIconDataUrl;
      faviconCache.set(url, { url: iconUrl, timestamp: Date.now() });
      return iconUrl;
    }

    // Limpar URL para extrair o domínio
    let domain = null;
    try {
      domain = new URL(url).hostname;
      if (!domain) return starIconDataUrl;
    } catch (error) {
      console.error("Erro ao processar URL:", error);
      return starIconDataUrl;
    }

    // Adicionar um parâmetro de cache-busting para forçar o recarregamento
    const cacheBuster = new Date().getTime();
    const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=32&cb=${cacheBuster}`;
    
    // Armazenar no cache com timestamp
    faviconCache.set(url, { url: faviconUrl, timestamp: Date.now() });
    
    // Verificar se o cache está muito grande e limpar se necessário
    if (faviconCache.size > MAX_FAVICON_CACHE_SIZE) {
      pruneFaviconCache();
    }
    
    return faviconUrl;
  } catch (error) {
    return starIconDataUrl;
  }
}

// Função que carrega favicon de forma assíncrona e com delay controlado
function loadFaviconWithDelay(imgElement, url, delay = 10) {
  // Definir a estrela SVG como placeholder inicialmente
  imgElement.src = starIconDataUrl;
  
  // Carregar o favicon real com um pequeno atraso para melhorar performance
  setTimeout(() => {
    try {
      // Usar requestIdleCallback se disponível para melhorar performance
      if (window.requestIdleCallback) {
        requestIdleCallback(() => {
          imgElement.src = getFaviconUrl(url);
        });
      } else {
      imgElement.src = getFaviconUrl(url);
      }
    } catch (e) {
      console.warn("Erro ao carregar favicon:", e);
      imgElement.src = starIconDataUrl;
    }
  }, delay);
}

function createBookmarkElement(bookmark, folderId) {
  // Verificar se já existe no cache e tentar reutilizar
  if (bookmarkElementCache.has(bookmark.id)) {
    const cachedItem = bookmarkElementCache.get(bookmark.id);
    
    // Atualizar propriedades do elemento
    cachedItem.dataset.index = bookmark.index;
    cachedItem.dataset.folder = folderId;
    
    // Verificar se é URL interna e marcar adequadamente
    if (bookmark.url) {
      const url = bookmark.url.toLowerCase();
      if (url.startsWith('chrome://')) {
        cachedItem.dataset.internalUrl = 'chrome';
      } else if (url.startsWith('edge://')) {
        cachedItem.dataset.internalUrl = 'edge';
      } else if (url.startsWith('about:')) {
        cachedItem.dataset.internalUrl = 'about';
      } else if (url.startsWith('browser://')) {
        cachedItem.dataset.internalUrl = 'browser';
      } else {
        delete cachedItem.dataset.internalUrl;
      }
    }
    
    // Atualiza o status de seleção
    if (selectedBookmarkIds.has(bookmark.id)) {
      cachedItem.classList.add("selected");
      const checkbox = cachedItem.querySelector('.bookmark-checkbox');
      if (checkbox) checkbox.checked = true;
    } else {
      cachedItem.classList.remove("selected");
      const checkbox = cachedItem.querySelector('.bookmark-checkbox');
      if (checkbox) checkbox.checked = false;
    }
    
    // Atualizar o link e texto, mas sempre recarregar o favicon
    const titleLink = cachedItem.querySelector('.bookmark-link');
    if (titleLink) {
      titleLink.textContent = bookmark.title || bookmark.url;
      titleLink.href = bookmark.url;
      titleLink.title = bookmark.title || bookmark.url;
    }

    // Atualizar URL container
    const urlContainer = cachedItem.querySelector('.url-container');
    if (urlContainer) {
      urlContainer.textContent = bookmark.url;
    }



    // Atualizar favicon
    const faviconContainer = cachedItem.querySelector('.favicon-container');
    if (faviconContainer) {
      // Remover favicon antigo, se houver
      const oldFavicon = faviconContainer.querySelector('.favicon');
      if (oldFavicon) oldFavicon.remove();
      // Criar novo favicon
      const newFavicon = document.createElement("img");
      newFavicon.className = "favicon";
      newFavicon.alt = "";
      newFavicon.setAttribute("draggable", "false");
      newFavicon.onerror = function() {
        this.src = starIconDataUrl;
        this.onerror = null;
      };
      const batchDelay = (faviconBatchCounter++ % 10) * 20;
      loadFaviconWithDelay(newFavicon, bookmark.url, batchDelay);
      faviconContainer.appendChild(newFavicon);
    }
    
    return cachedItem;
  }

  // Criar novo elemento se não estiver no cache
  const item = document.createElement("div");
  item.className = "bookmark-item";
  item.dataset.id = bookmark.id;
  item.dataset.index = bookmark.index;
  item.dataset.folder = folderId;
  // Drag & drop removido
  
  // Verificar se é URL interna e marcar adequadamente
  if (bookmark.url) {
    const url = bookmark.url.toLowerCase();
    if (url.startsWith('chrome://')) {
      item.dataset.internalUrl = 'chrome';
    } else if (url.startsWith('edge://')) {
      item.dataset.internalUrl = 'edge';
    } else if (url.startsWith('about:')) {
      item.dataset.internalUrl = 'about';
    } else if (url.startsWith('browser://')) {
      item.dataset.internalUrl = 'browser';
    }
  }
  
  // Criar div estrutural para checkbox
  const checkboxContainer = document.createElement("div");
  checkboxContainer.className = "checkbox-container";
  checkboxContainer.setAttribute("draggable", "false");

  // Adicionar checkbox para seleção
  const checkbox = document.createElement("input");
  checkbox.type = "checkbox";
  checkbox.className = "bookmark-checkbox";
  checkbox.setAttribute("draggable", "false");
  // Permitir eventos de ponteiro no checkbox para funcionar com nossa lógica de seleção
  // checkbox.style.pointerEvents = "none"; // REMOVIDO: Estava impedindo a seleção

  checkbox.addEventListener("change", (e) => {
    console.log("Render.js: Checkbox change event disparado para", bookmark.id);
    // Remover stopPropagation para não interferir com outros listeners
    // e.stopPropagation();

    if (checkbox.checked) {
      selectedBookmarkIds.add(bookmark.id);
      item.classList.add("selected", "sortable-selected");
    } else {
      selectedBookmarkIds.delete(bookmark.id);
      item.classList.remove("selected", "sortable-selected");
    }

    updateSelectedBookmarksCount();
  });

  checkboxContainer.appendChild(checkbox);
  
  // Aplica a classe 'selected' se o item estiver na lista de selecionados
  if (selectedBookmarkIds.has(bookmark.id)) {
    item.classList.add("selected", "sortable-selected");
    checkbox.checked = true;
  }

  // Criar div estrutural para favicon
  const faviconContainer = document.createElement("div");
  faviconContainer.className = "favicon-container";
  faviconContainer.setAttribute("draggable", "false");

  // Adiciona favicon com carregamento otimizado
  const favicon = document.createElement("img");
  favicon.className = "favicon";
  favicon.alt = "";
  favicon.setAttribute("draggable", "false");

  // Configurar tratamento de erro
  favicon.onerror = function() {
    this.src = starIconDataUrl;
    this.onerror = null;
  };

  // Carregar o favicon com um atraso escalonado para evitar sobrecarga
  const batchDelay = (faviconBatchCounter++ % 10) * 20;
  loadFaviconWithDelay(favicon, bookmark.url, batchDelay);

  faviconContainer.appendChild(favicon);

  // Criar div estrutural para o título (link)
  const titleContainer = document.createElement("div");
  titleContainer.className = "title-container";
  titleContainer.setAttribute("draggable", "false");

  const link = document.createElement("a");
  link.href = bookmark.url;
  link.target = "_blank";
  link.textContent = bookmark.title || bookmark.url;
  // Usar o título completo como tooltip, ou a URL se o título estiver vazio
  link.title = bookmark.title || bookmark.url;
  link.className = "bookmark-link";
  link.setAttribute("draggable", "false"); // Impede arrasto pelo link

  titleContainer.appendChild(link);

  // Criar div para exibir URL em cinza (como texto, não link)
  const urlContainer = document.createElement("div");
  urlContainer.className = "url-container";
  urlContainer.textContent = bookmark.url;
  urlContainer.setAttribute("draggable", "false");

  const removeBtn = document.createElement("button");
  removeBtn.className = "remove-btn";
  removeBtn.title = "Remover";
  removeBtn.setAttribute("draggable", "false");
  
  // Adiciona o ícone SVG diretamente no DOM
  const svgNS = "http://www.w3.org/2000/svg";
  const svgElem = document.createElementNS(svgNS, "svg");
  svgElem.setAttribute("width", "16");
  svgElem.setAttribute("height", "16");
  svgElem.setAttribute("viewBox", "0 0 20 20");
  svgElem.setAttribute("class", "remove-icon");
  svgElem.setAttribute("aria-hidden", "true");
  
  const path = document.createElementNS(svgNS, "path");
  path.setAttribute("d", "M4.09 4.22l.06-.07a.5.5 0 01.63-.06l.07.06L10 9.29l5.15-5.14a.5.5 0 01.63-.06l.07.06c.18.17.2.44.06.63l-.06.07L10.71 10l5.14 5.15c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L10 10.71l-5.15 5.14a.5.5 0 01-.63.06l-.07-.06a.5.5 0 01-.06-.63l.06-.07L9.29 10 4.15 4.85a.5.5 0 01-.06-.63l.06-.07-.06.07z");
  path.setAttribute("fill-rule", "nonzero");
  
  svgElem.appendChild(path);
  removeBtn.appendChild(svgElem);
  
  removeBtn.addEventListener("click", (e) => {
    e.stopPropagation();
    if (confirm("Deseja remover este favorito?")) {
      chrome.bookmarks.remove(bookmark.id, () => {
        item.remove();
        bookmarkElementCache.delete(bookmark.id);
        selectedBookmarkIds.delete(bookmark.id);
        updateBookmarkCount();
      });
    }
  });

  const actions = document.createElement("div");
  actions.className = "actions";
  actions.append(removeBtn);

  // Criar container para título e URL (dividem espaço 50/50)
  const textContainer = document.createElement("div");
  textContainer.className = "text-container";
  textContainer.append(titleContainer, urlContainer);

  // Criar container para elementos da esquerda
  const leftContainer = document.createElement("div");
  leftContainer.className = "left-container";
  leftContainer.append(checkboxContainer, faviconContainer, textContainer);

  // Estrutura: container esquerdo + actions
  item.append(leftContainer, actions);

  // Função auxiliar para alternar seleção do item
  const toggleItemSelection = (e) => {
    e.stopPropagation();

    if (selectedBookmarkIds.has(bookmark.id)) {
      selectedBookmarkIds.delete(bookmark.id);
      item.classList.remove("selected");
      checkbox.checked = false;
    } else {
      selectedBookmarkIds.add(bookmark.id);
      item.classList.add("selected");
      checkbox.checked = true;
    }

    updateSelectedBookmarksCount();
  };

  // REMOVIDO: Event listeners de clique movidos para sortable.js para evitar conflitos
  // O sortable.js agora gerencia todos os eventos de clique dos bookmarks

  // O titleContainer deve abrir o link quando clicado, não selecionar
  titleContainer.addEventListener("click", (e) => {
    // Permitir que o link seja aberto normalmente
    // Não chamar e.stopPropagation() para permitir que o evento chegue ao link
  });

  // Sistema de drag & drop removido
  
  // Armazenar no cache para reutilização futura
  bookmarkElementCache.set(bookmark.id, item);
  return item;
}

// Função setupDragEvents removida - Sistema de drag & drop desabilitado

// Função para limpar todos os favoritos visíveis e o contador
function clearAllBookmarks() {
  bookmarksContainer.innerHTML = "";
  updateBookmarksDisplayCount();
  
  // Não limpar o cache completamente, apenas remover elementos que não estão mais visíveis
  const visibleIds = new Set();
  document.querySelectorAll(".bookmark-item").forEach(el => {
    if (el.dataset.id) {
      visibleIds.add(el.dataset.id);
    }
  });
  
  // Manter apenas os itens que ainda estão visíveis
  for (const [id, element] of bookmarkElementCache.entries()) {
    if (!visibleIds.has(id)) {
      bookmarkElementCache.delete(id);
    }
  }
}

/**
 * Renderiza os favoritos no container especificado
 * @param {Array} bookmarks - Lista de favoritos para renderizar
 * @param {HTMLElement} container - Container onde os favoritos serão renderizados
 * @param {boolean} append - Se true, adiciona ao final; se false, substitui o conteúdo
 * @param {string} folderId - ID da pasta dos favoritos
 * @param {boolean} preserveCache - Se true, preserva o cache atual
 */
function renderBookmarks(bookmarks, container, append = false, folderId, preserveCache = false) {
  // Verificar se a renderização está bloqueada
  if (blockRendering) {
    console.log(`Renderização bloqueada para pasta ${folderId}, ignorando solicitação de ${bookmarks?.length || 0} favoritos`);
    return;
  }

  // Verificar se já existe uma renderização em andamento
  if (renderInProgress) {
    console.log("Renderização já em andamento, enfileirando nova solicitação");
    setTimeout(() => {
      // Verificar novamente se a renderização foi bloqueada entretanto
      if (blockRendering) {
        console.log(`Renderização bloqueada durante espera para pasta ${folderId}, cancelando solicitação enfileirada`);
        return;
      }
      renderBookmarks(bookmarks, container, append, folderId, preserveCache);
    }, 100);
    return;
  }

  renderInProgress = true;

  try {
    // Se não for para adicionar ao final, limpar o container
    if (!append) {
      // Remover apenas os elementos desta pasta
      if (folderId) {
        const elementsToRemove = container.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`);
        elementsToRemove.forEach(el => el.remove());
      } else {
        container.innerHTML = "";
      }
    }

    // Se não há favoritos, atualizar contagem e sair
    if (!bookmarks || bookmarks.length === 0) {
      updateBookmarksDisplayCount();
      renderInProgress = false;
      return;
    }
    
    // Resetar o contador de lotes de favicons
    faviconBatchCounter = 0;
    
    // Usar DocumentFragment para melhor performance
    const fragment = document.createDocumentFragment();
    
    // Filtrar apenas favoritos (com URL)
    const validBookmarks = bookmarks.filter(bookmark => bookmark.url);
    
    // Tamanho do lote para renderização
    const batchSize = 20;
    
    // Renderizar em lotes para não bloquear a UI
    renderBatch(validBookmarks, 0, batchSize, () => {
      // Atualizar a contagem após renderizar todos
      updateBookmarksDisplayCount();
      renderInProgress = false;
    });
    
    /**
     * Renderiza um lote de favoritos
     * @param {Array} bookmarkBatch - Lote de favoritos
     * @param {number} startIndex - Índice inicial
     * @param {number} batchSize - Tamanho do lote
     * @param {Function} onComplete - Função chamada ao completar todos os lotes
     */
    function renderBatch(bookmarkBatch, startIndex, batchSize, onComplete) {
      // Verificar se ainda há favoritos para renderizar
      if (startIndex >= bookmarkBatch.length) {
        if (onComplete) onComplete();
        return;
      }
      
      // Calcular o índice final do lote atual
      const endIndex = Math.min(startIndex + batchSize, bookmarkBatch.length);
      
      // Criar um fragmento para este lote
      const batchFragment = document.createDocumentFragment();
      
      // Renderizar este lote
      for (let i = startIndex; i < endIndex; i++) {
        const bookmark = bookmarkBatch[i];
        
        // Verificar se o bookmark é válido
        if (!bookmark || !bookmark.id) continue;
        
        try {
          const element = createBookmarkElement(bookmark, folderId);
          if (element) {
            batchFragment.appendChild(element);
          }
        } catch (error) {
          console.error("Erro ao criar elemento de favorito:", error);
        }
      }
      
      // Adicionar o fragmento ao container
      container.appendChild(batchFragment);
      
      // Agendar o próximo lote usando requestAnimationFrame para melhor performance
      requestAnimationFrame(() => {
          renderBatch(bookmarkBatch, endIndex, batchSize, onComplete);
      });
    }
  } catch (error) {
    console.error("Erro ao renderizar favoritos:", error);
    renderInProgress = false;
  }
}

function updateBookmarksDisplayCount() {
  try {
    // Verificação rigorosa: se não há pastas selecionadas, zerar contador
    if (selectedFolderIds.size === 0) {
      const countEl = document.getElementById("bookmarksDisplayCount");
      countEl.textContent = "Favoritos exibidos: 0";
      countEl.classList.remove("has-bookmarks");
      
      // Garantir que não haja elementos visíveis
      if (bookmarksContainer.children.length > 0) {
        clearAllBookmarks();
      }
      return;
    }
    
    // Conta apenas os itens realmente visíveis e de pastas selecionadas
    const visibleItems = document.querySelectorAll(".bookmark-item");
    const total = Array.from(visibleItems).filter(item => 
      item.style.display !== "none" && 
      // Verifica se o item é da pasta selecionada
      selectedFolderIds.has(item.dataset.folder)
    ).length;
    
    // Atualiza o contador principal
    const countEl = document.getElementById("bookmarksDisplayCount");
    countEl.textContent = `Favoritos exibidos: ${total === 0 ? '0' : total}`;
      
      // Adiciona classe visual quando há favoritos exibidos
      if (total > 0) {
      countEl.classList.add("has-bookmarks");
      } else {
      countEl.classList.remove("has-bookmarks");
    }
    
    // Verificação adicional: se o contador é zero, mas há elementos visíveis, há inconsistência
    // Limpar tudo para garantir a sincronização
    if (total === 0 && bookmarksContainer.children.length > 0) {
      bookmarksContainer.innerHTML = '';
    }
  } catch (error) {
    console.error("Erro ao atualizar contagem de favoritos:", error);
    // Em caso de erro, zera o contador e limpa tudo
    const countEl = document.getElementById("bookmarksDisplayCount");
    countEl.textContent = "Favoritos exibidos: 0";
    countEl.classList.remove("has-bookmarks");
    
    bookmarksContainer.innerHTML = '';
  }
}

// Adicionar a função updateFolderContents
/**
 * Atualiza o conteúdo da pasta selecionada
 * @param {string} folderId - ID da pasta a ser carregada
 * @param {boolean} reuseCache - Se deve reutilizar elementos em cache
 */
function updateFolderContents(folderId, reuseCache = true) {
  if (blockRendering) {
    console.log("Renderização bloqueada, ignorando updateFolderContents");
    return;
  }
  
  // Evitar múltiplas renderizações simultâneas
  if (renderInProgress) {
    console.log("Renderização já em andamento, enfileirando nova atualização");
    setTimeout(() => updateFolderContents(folderId, reuseCache), 100);
    return;
  }
  
  renderInProgress = true;
  
  chrome.bookmarks.getChildren(folderId, (children) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao obter favoritos:", chrome.runtime.lastError);
      renderInProgress = false;
      return;
    }
    
    // Filtrar apenas favoritos com URL
    const bookmarks = children.filter(bookmark => bookmark.url);
    
    renderBookmarks(bookmarks, folderId, reuseCache);
    renderInProgress = false;
  });
}