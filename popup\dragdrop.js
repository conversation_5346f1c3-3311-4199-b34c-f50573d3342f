/**
 * <PERSON><PERSON><PERSON> de Drag and Drop para favoritos e pastas
 * Permite arrastar elementos entre colunas e dentro da mesma coluna
 */

let draggedElement = null;
let draggedElementType = null; // 'bookmark' ou 'folder'
let originalParentId = null;
let originalIndex = null;
let dropTarget = null;
let dropPosition = null; // 'before', 'after', 'inside'

/**
 * Inicializa o sistema de drag and drop
 */
function initDragAndDrop() {
  setupBookmarkDragEvents();
  setupFolderDragEvents();
}

/**
 * Configura eventos de drag para favoritos
 */
function setupBookmarkDragEvents() {
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  
  // Delegação de eventos para lidar com elementos dinâmicos
  bookmarksContainer.addEventListener('dragstart', handleBookmarkDragStart);
  bookmarksContainer.addEventListener('dragend', handleBookmarkDragEnd);
  bookmarksContainer.addEventListener('dragover', handleDragOver);
  bookmarksContainer.addEventListener('dragleave', handleDragLeave);
  bookmarksContainer.addEventListener('drop', handleDrop);
}

/**
 * Configura eventos de drag para pastas
 */
function setupFolderDragEvents() {
  const folderContainer = document.getElementById('folderCheckboxes');
  
  // Delegação de eventos para lidar com elementos dinâmicos
  folderContainer.addEventListener('dragstart', handleFolderDragStart);
  folderContainer.addEventListener('dragend', handleFolderDragEnd);
  folderContainer.addEventListener('dragover', handleDragOver);
  folderContainer.addEventListener('dragleave', handleDragLeave);
  folderContainer.addEventListener('drop', handleDrop);
}

/**
 * Manipula o início do arrasto de um favorito
 */
function handleBookmarkDragStart(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;
  
  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;
  
  draggedElement = target;
  draggedElementType = 'bookmark';
  originalParentId = target.dataset.folder;
  originalIndex = parseInt(target.dataset.index, 10);
  
  // Adicionar classe para estilo durante o arrasto
  target.classList.add('dragging');
  
  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', target.dataset.id);
  e.dataTransfer.effectAllowed = 'move';
  
  // Definir imagem de arrasto personalizada (opcional)
  const dragIcon = document.createElement('div');
  dragIcon.classList.add('drag-icon');
  dragIcon.textContent = target.querySelector('.bookmark-title').textContent;
  document.body.appendChild(dragIcon);
  e.dataTransfer.setDragImage(dragIcon, 0, 0);
  setTimeout(() => document.body.removeChild(dragIcon), 0);
}

/**
 * Manipula o fim do arrasto de um favorito
 */
function handleBookmarkDragEnd(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;
  
  // Remover classe de estilo
  target.classList.remove('dragging');
  
  // Limpar variáveis
  draggedElement = null;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  
  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o início do arrasto de uma pasta
 */
function handleFolderDragStart(e) {
  const target = e.target.closest('.folder-item');
  if (!target) return;
  
  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;
  
  draggedElement = target;
  draggedElementType = 'folder';
  originalParentId = target.dataset.parentId;
  originalIndex = parseInt(target.dataset.index, 10);
  
  // Adicionar classe para estilo durante o arrasto
  target.classList.add('dragging');
  
  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', target.dataset.id);
  e.dataTransfer.effectAllowed = 'move';
}

/**
 * Manipula o fim do arrasto de uma pasta
 */
function handleFolderDragEnd(e) {
  const target = e.target.closest('.folder-item');
  if (!target) return;
  
  // Remover classe de estilo
  target.classList.remove('dragging');
  
  // Limpar variáveis
  draggedElement = null;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  
  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o evento dragover para mostrar onde o elemento pode ser solto
 */
function handleDragOver(e) {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';
  
  // Identificar o alvo potencial de drop
  const bookmarkTarget = e.target.closest('.bookmark-item');
  const folderTarget = e.target.closest('.folder-item');
  
  // Remover indicadores anteriores
  removeDropIndicators();
  
  if (bookmarkTarget) {
    // Determinar posição (antes ou depois)
    const rect = bookmarkTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;
    
    if (e.clientY < midY) {
      // Antes do favorito
      bookmarkTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else {
      // Depois do favorito
      bookmarkTarget.classList.add('drop-after');
      dropPosition = 'after';
    }
    
    dropTarget = bookmarkTarget;
  } else if (folderTarget) {
    // Determinar posição (antes, depois ou dentro)
    const rect = folderTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;
    const threshold = rect.height * 0.25; // 25% da altura para determinar "dentro"
    
    if (e.clientY < midY - threshold) {
      // Antes da pasta
      folderTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else if (e.clientY > midY + threshold) {
      // Depois da pasta
      folderTarget.classList.add('drop-after');
      dropPosition = 'after';
    } else {
      // Dentro da pasta
      folderTarget.classList.add('drop-inside');
      dropPosition = 'inside';
    }
    
    dropTarget = folderTarget;
  }
}

/**
 * Manipula o evento dragleave para remover indicadores visuais
 */
function handleDragLeave(e) {
  // Verificar se realmente saiu do elemento
  const relatedTarget = e.relatedTarget;
  if (relatedTarget && e.currentTarget.contains(relatedTarget)) {
    return; // Ainda dentro do container
  }
  
  removeDropIndicators();
}

/**
 * Remove todos os indicadores visuais de drop
 */
function removeDropIndicators() {
  document.querySelectorAll('.drop-before, .drop-after, .drop-inside').forEach(el => {
    el.classList.remove('drop-before', 'drop-after', 'drop-inside');
  });
}

/**
 * Manipula o evento drop para mover o elemento
 */
function handleDrop(e) {
  e.preventDefault();
  
  // Remover indicadores visuais
  removeDropIndicators();
  
  // Se não há elemento arrastado ou alvo de drop, sair
  if (!draggedElement || !dropTarget || !dropPosition) return;
  
  const draggedId = draggedElement.dataset.id;
  const targetId = dropTarget.dataset.id;
  
  // Não permitir soltar em si mesmo
  if (draggedId === targetId && dropPosition !== 'inside') return;
  
  // Determinar nova posição e parent ID
  let newParentId, newIndex;
  
  if (draggedElementType === 'bookmark') {
    if (dropTarget.classList.contains('bookmark-item')) {
      // Favorito para favorito
      newParentId = dropTarget.dataset.folder;
      
      // Obter índice do alvo
      const targetIndex = parseInt(dropTarget.dataset.index, 10);
      
      // Ajustar índice com base na posição
      if (dropPosition === 'before') {
        newIndex = targetIndex;
      } else if (dropPosition === 'after') {
        newIndex = targetIndex + 1;
      }
      
      // Mover o favorito na API do Chrome
      moveBookmark(draggedId, newParentId, newIndex);
    } else if (dropTarget.classList.contains('folder-item') && dropPosition === 'inside') {
      // Favorito para dentro de pasta
      newParentId = targetId;
      
      // Mover para o final da pasta
      moveBookmark(draggedId, newParentId);
    }
  } else if (draggedElementType === 'folder') {
    if (dropTarget.classList.contains('folder-item')) {
      // Pasta para pasta
      if (dropPosition === 'inside') {
        // Mover para dentro da pasta
        newParentId = targetId;
        moveFolder(draggedId, newParentId);
      } else {
        // Mover antes ou depois da pasta
        newParentId = dropTarget.dataset.parentId;
        
        // Obter índice do alvo
        const targetIndex = parseInt(dropTarget.dataset.index, 10);
        
        // Ajustar índice com base na posição
        if (dropPosition === 'before') {
          newIndex = targetIndex;
        } else if (dropPosition === 'after') {
          newIndex = targetIndex + 1;
        }
        
        moveFolder(draggedId, newParentId, newIndex);
      }
    }
  }
}

/**
 * Move um favorito para nova posição usando a API do Chrome
 */
function moveBookmark(bookmarkId, newParentId, newIndex) {
  const moveProperties = { parentId: newParentId };
  
  // Adicionar índice apenas se especificado
  if (newIndex !== undefined) {
    moveProperties.index = newIndex;
  }
  
  chrome.bookmarks.move(bookmarkId, moveProperties, (result) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao mover favorito:", chrome.runtime.lastError);
      showActionFeedback("Erro ao mover favorito", "error");
      return;
    }
    
    // Atualizar UI
    if (selectedFolderIds.has(newParentId) || selectedFolderIds.has(originalParentId)) {
      reloadSelectedFolders();
    }
    
    showActionFeedback("Favorito movido com sucesso", "success");
  });
}

/**
 * Move uma pasta para nova posição usando a API do Chrome
 */
function moveFolder(folderId, newParentId, newIndex) {
  // Verificar se não está tentando mover para dentro de si mesmo
  if (folderId === newParentId) {
    showActionFeedback("Não é possível mover uma pasta para dentro dela mesma", "error");
    return;
  }
  
  // Verificar se não está tentando mover para dentro de um descendente
  chrome.bookmarks.getSubTree(folderId, (results) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao verificar árvore:", chrome.runtime.lastError);
      return;
    }
    
    // Função recursiva para verificar se o destino é descendente
    function isDescendant(node, targetId) {
      if (node.id === targetId) return true;
      if (node.children) {
        for (const child of node.children) {
          if (isDescendant(child, targetId)) return true;
        }
      }
      return false;
    }
    
    // Se o destino for descendente, abortar
    if (results[0] && isDescendant(results[0], newParentId)) {
      showActionFeedback("Não é possível mover uma pasta para dentro de sua subpasta", "error");
      return;
    }
    
    // Prosseguir com a movimentação
    const moveProperties = { parentId: newParentId };
    
    // Adicionar índice apenas se especificado
    if (newIndex !== undefined) {
      moveProperties.index = newIndex;
    }
    
    chrome.bookmarks.move(folderId, moveProperties, (result) => {
      if (chrome.runtime.lastError) {
        console.error("Erro ao mover pasta:", chrome.runtime.lastError);
        showActionFeedback("Erro ao mover pasta", "error");
        return;
      }
      
      // Recarregar a árvore de pastas
      loadBookmarkFolders();
      
      // Atualizar UI se a pasta movida ou destino estiver selecionada
      if (selectedFolderIds.has(folderId) || selectedFolderIds.has(newParentId)) {
        reloadSelectedFolders();
      }
      
      showActionFeedback("Pasta movida com sucesso", "success");
    });
  });
}