/**
 * Siste<PERSON> de Drag and Drop para favoritos e pastas
 * Permite arrastar elementos entre colunas e dentro da mesma coluna
 */

let draggedElement = null;
let draggedElementType = null; // 'bookmark' ou 'folder'
let originalParentId = null;
let originalIndex = null;
let dropTarget = null;
let dropPosition = null; // 'before', 'after', 'inside'
let dragStartX = 0;
let dragStartY = 0;
let isDragging = false;

/**
 * Inicializa o sistema de drag and drop
 */
function initDragAndDrop() {
  console.log("Inicializando sistema de drag and drop...");

  // Verificar se os containers existem
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  const folderContainer = document.getElementById('folderCheckboxes');

  console.log("Containers encontrados:", {
    bookmarks: !!bookmarksContainer,
    folders: !!folderContainer
  });

  setupBookmarkDragEvents();
  setupFolderDragEvents();
  setupDragStyles();

  // Adicionar um teste para verificar se elementos draggable são criados
  setTimeout(() => {
    const draggableBookmarks = document.querySelectorAll('.bookmark-item[draggable="true"]');
    const draggableFolders = document.querySelectorAll('.folder-option[draggable="true"]');

    console.log("Elementos draggable encontrados:", {
      bookmarks: draggableBookmarks.length,
      folders: draggableFolders.length
    });
  }, 2000);
}

/**
 * Configura estilos CSS necessários para o drag and drop
 */
function setupDragStyles() {
  const style = document.createElement('style');
  style.textContent = `
    /* Estilos para drag and drop */
    .dragging {
      opacity: 0.5;
      transform: rotate(2deg);
      z-index: 1000;
    }

    .drop-before::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #0078d4;
      z-index: 10;
    }

    .drop-after::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #0078d4;
      z-index: 10;
    }

    .drop-inside {
      background-color: rgba(0, 120, 212, 0.1) !important;
      border: 2px dashed #0078d4 !important;
    }

    .drag-ghost {
      position: absolute;
      background: rgba(0, 120, 212, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      pointer-events: none;
      z-index: 1001;
    }

    .bookmark-item, .folder-option {
      position: relative;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Configura eventos de drag para favoritos
 */
function setupBookmarkDragEvents() {
  const bookmarksContainer = document.getElementById('bookmarksContainer');

  if (!bookmarksContainer) {
    console.error("Container de favoritos não encontrado!");
    return;
  }

  console.log("Configurando eventos de drag para favoritos...");

  // Delegação de eventos para lidar com elementos dinâmicos
  bookmarksContainer.addEventListener('dragstart', handleBookmarkDragStart);
  bookmarksContainer.addEventListener('dragend', handleBookmarkDragEnd);
  bookmarksContainer.addEventListener('dragover', handleDragOver);
  bookmarksContainer.addEventListener('dragleave', handleDragLeave);
  bookmarksContainer.addEventListener('drop', handleDrop);
}

/**
 * Configura eventos de drag para pastas
 */
function setupFolderDragEvents() {
  const folderContainer = document.getElementById('folderCheckboxes');

  if (!folderContainer) {
    console.error("Container de pastas não encontrado!");
    return;
  }

  console.log("Configurando eventos de drag para pastas...");

  // Delegação de eventos para lidar com elementos dinâmicos
  folderContainer.addEventListener('dragstart', handleFolderDragStart);
  folderContainer.addEventListener('dragend', handleFolderDragEnd);
  folderContainer.addEventListener('dragover', handleDragOver);
  folderContainer.addEventListener('dragleave', handleDragLeave);
  folderContainer.addEventListener('drop', handleDrop);
}

/**
 * Manipula o início do arrasto de um favorito
 */
function handleBookmarkDragStart(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;

  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;

  console.log("Iniciando drag de favorito:", target.dataset.id);

  draggedElement = target;
  draggedElementType = 'bookmark';
  originalParentId = target.dataset.folder;
  originalIndex = parseInt(target.dataset.index, 10);
  isDragging = true;

  dragStartX = e.clientX;
  dragStartY = e.clientY;

  // Adicionar classe para estilo durante o arrasto
  target.classList.add('dragging');

  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', target.dataset.id);
  e.dataTransfer.effectAllowed = 'move';

  // Criar imagem de arrasto personalizada
  const bookmarkLink = target.querySelector('.bookmark-link');
  const bookmarkTitle = bookmarkLink ? bookmarkLink.textContent : 'Favorito';

  const dragIcon = document.createElement('div');
  dragIcon.classList.add('drag-ghost');
  dragIcon.textContent = bookmarkTitle;
  document.body.appendChild(dragIcon);

  // Posicionar o ghost fora da tela inicialmente
  dragIcon.style.left = '-1000px';
  dragIcon.style.top = '-1000px';

  e.dataTransfer.setDragImage(dragIcon, 10, 10);

  // Remover o ghost após um pequeno delay
  setTimeout(() => {
    if (document.body.contains(dragIcon)) {
      document.body.removeChild(dragIcon);
    }
  }, 0);
}

/**
 * Manipula o fim do arrasto de um favorito
 */
function handleBookmarkDragEnd(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;

  console.log("Finalizando drag de favorito");

  // Remover classe de estilo
  target.classList.remove('dragging');

  // Limpar variáveis
  draggedElement = null;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  isDragging = false;

  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o início do arrasto de uma pasta
 */
function handleFolderDragStart(e) {
  const target = e.target.closest('.folder-option');
  if (!target) return;

  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;

  console.log("Iniciando drag de pasta");

  // Obter o ID da pasta do checkbox
  const checkbox = target.querySelector('.folder-checkbox');
  if (!checkbox) return;

  const folderId = checkbox.value;

  draggedElement = target;
  draggedElementType = 'folder';
  originalParentId = target.dataset.parentId || '0'; // Root se não especificado
  originalIndex = parseInt(target.dataset.index, 10) || 0;
  isDragging = true;

  dragStartX = e.clientX;
  dragStartY = e.clientY;

  // Adicionar classe para estilo durante o arrasto
  target.classList.add('dragging');

  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', folderId);
  e.dataTransfer.effectAllowed = 'move';

  // Criar imagem de arrasto personalizada
  const folderTitle = target.querySelector('.folder-title');
  const folderName = folderTitle ? folderTitle.textContent : 'Pasta';

  const dragIcon = document.createElement('div');
  dragIcon.classList.add('drag-ghost');
  dragIcon.textContent = folderName;
  document.body.appendChild(dragIcon);

  // Posicionar o ghost fora da tela inicialmente
  dragIcon.style.left = '-1000px';
  dragIcon.style.top = '-1000px';

  e.dataTransfer.setDragImage(dragIcon, 10, 10);

  // Remover o ghost após um pequeno delay
  setTimeout(() => {
    if (document.body.contains(dragIcon)) {
      document.body.removeChild(dragIcon);
    }
  }, 0);
}

/**
 * Manipula o fim do arrasto de uma pasta
 */
function handleFolderDragEnd(e) {
  const target = e.target.closest('.folder-option');
  if (!target) return;

  console.log("Finalizando drag de pasta");

  // Remover classe de estilo
  target.classList.remove('dragging');

  // Limpar variáveis
  draggedElement = null;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  isDragging = false;

  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o evento dragover para mostrar onde o elemento pode ser solto
 */
function handleDragOver(e) {
  if (!isDragging) return;

  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';

  // Identificar o alvo potencial de drop
  const bookmarkTarget = e.target.closest('.bookmark-item');
  const folderTarget = e.target.closest('.folder-option');

  // Remover indicadores anteriores
  removeDropIndicators();

  // Não permitir drop no próprio elemento
  if ((bookmarkTarget && bookmarkTarget === draggedElement) ||
      (folderTarget && folderTarget === draggedElement)) {
    return;
  }

  if (bookmarkTarget && draggedElementType === 'bookmark') {
    // Favorito sobre favorito - determinar posição (antes ou depois)
    const rect = bookmarkTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;

    if (e.clientY < midY) {
      // Antes do favorito
      bookmarkTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else {
      // Depois do favorito
      bookmarkTarget.classList.add('drop-after');
      dropPosition = 'after';
    }

    dropTarget = bookmarkTarget;
  } else if (folderTarget) {
    // Elemento sobre pasta - determinar posição (antes, depois ou dentro)
    const rect = folderTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;
    const threshold = rect.height * 0.3; // 30% da altura para determinar "dentro"

    if (e.clientY < midY - threshold) {
      // Antes da pasta
      folderTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else if (e.clientY > midY + threshold) {
      // Depois da pasta
      folderTarget.classList.add('drop-after');
      dropPosition = 'after';
    } else {
      // Dentro da pasta (apenas para favoritos)
      if (draggedElementType === 'bookmark') {
        folderTarget.classList.add('drop-inside');
        dropPosition = 'inside';
      } else {
        // Para pastas, não permitir drop "dentro" por enquanto
        folderTarget.classList.add('drop-after');
        dropPosition = 'after';
      }
    }

    dropTarget = folderTarget;
  } else {
    // Limpar se não há alvo válido
    dropTarget = null;
    dropPosition = null;
  }
}

/**
 * Manipula o evento dragleave para remover indicadores visuais
 */
function handleDragLeave(e) {
  // Verificar se realmente saiu do elemento
  const relatedTarget = e.relatedTarget;
  if (relatedTarget && e.currentTarget.contains(relatedTarget)) {
    return; // Ainda dentro do container
  }
  
  removeDropIndicators();
}

/**
 * Remove todos os indicadores visuais de drop
 */
function removeDropIndicators() {
  document.querySelectorAll('.drop-before, .drop-after, .drop-inside').forEach(el => {
    el.classList.remove('drop-before', 'drop-after', 'drop-inside');
  });
}

/**
 * Manipula o evento drop para mover o elemento
 */
function handleDrop(e) {
  e.preventDefault();

  console.log("Drop detectado");

  // Remover indicadores visuais
  removeDropIndicators();

  // Se não há elemento arrastado ou alvo de drop, sair
  if (!draggedElement || !dropTarget || !dropPosition) {
    console.log("Drop cancelado: dados insuficientes");
    return;
  }

  // Obter IDs
  let draggedId;
  let targetId;

  if (draggedElementType === 'bookmark') {
    draggedId = draggedElement.dataset.id;
  } else if (draggedElementType === 'folder') {
    const checkbox = draggedElement.querySelector('.folder-checkbox');
    draggedId = checkbox ? checkbox.value : null;
  }

  if (dropTarget.classList.contains('bookmark-item')) {
    targetId = dropTarget.dataset.id;
  } else if (dropTarget.classList.contains('folder-option')) {
    const checkbox = dropTarget.querySelector('.folder-checkbox');
    targetId = checkbox ? checkbox.value : null;
  }

  if (!draggedId || !targetId) {
    console.log("Drop cancelado: IDs inválidos");
    return;
  }

  // Não permitir soltar em si mesmo
  if (draggedId === targetId && dropPosition !== 'inside') {
    console.log("Drop cancelado: mesmo elemento");
    return;
  }

  console.log(`Movendo ${draggedElementType} ${draggedId} para ${dropPosition} de ${targetId}`);

  // Determinar nova posição e parent ID
  let newParentId, newIndex;

  if (draggedElementType === 'bookmark') {
    if (dropTarget.classList.contains('bookmark-item')) {
      // Favorito para favorito - mover para a mesma pasta
      newParentId = dropTarget.dataset.folder;

      // Obter índice do alvo
      const targetIndex = parseInt(dropTarget.dataset.index, 10);

      // Ajustar índice com base na posição
      if (dropPosition === 'before') {
        newIndex = targetIndex;
      } else if (dropPosition === 'after') {
        newIndex = targetIndex + 1;
      }

      // Mover o favorito na API do Chrome
      moveBookmark(draggedId, newParentId, newIndex);
    } else if (dropTarget.classList.contains('folder-option') && dropPosition === 'inside') {
      // Favorito para dentro de pasta
      newParentId = targetId;

      // Mover para o final da pasta
      moveBookmark(draggedId, newParentId);
    }
  } else if (draggedElementType === 'folder') {
    if (dropTarget.classList.contains('folder-option')) {
      // Pasta para pasta - por enquanto, apenas reordenar no mesmo nível
      // TODO: Implementar movimentação hierárquica de pastas
      console.log("Movimentação de pastas ainda não implementada completamente");
      showActionFeedback("Movimentação de pastas em desenvolvimento", "info");
    }
  }
}

/**
 * Move um favorito para nova posição usando a API do Chrome
 */
function moveBookmark(bookmarkId, newParentId, newIndex) {
  console.log(`Movendo favorito ${bookmarkId} para pasta ${newParentId}, índice ${newIndex}`);

  const moveProperties = { parentId: newParentId };

  // Adicionar índice apenas se especificado
  if (newIndex !== undefined) {
    moveProperties.index = newIndex;
  }

  chrome.bookmarks.move(bookmarkId, moveProperties, () => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao mover favorito:", chrome.runtime.lastError);
      showActionFeedback("Erro ao mover favorito", "error");
      return;
    }

    console.log("Favorito movido com sucesso");

    // Atualizar UI - recarregar pastas afetadas
    if (typeof reloadSelectedFolders === 'function') {
      reloadSelectedFolders();
    } else if (typeof window.reloadSelectedFolders === 'function') {
      window.reloadSelectedFolders();
    } else {
      // Fallback: recarregar manualmente as pastas selecionadas
      if (typeof selectedFolderIds !== 'undefined') {
        selectedFolderIds.forEach(folderId => {
          chrome.bookmarks.getChildren(folderId, (children) => {
            if (!chrome.runtime.lastError) {
              const bookmarks = children.filter(child => child.url);
              renderBookmarks(bookmarks, bookmarksContainer, false, folderId, false);
            }
          });
        });
      }
    }

    showActionFeedback("Favorito movido com sucesso", "success");
  });
}

/**
 * Move uma pasta para nova posição usando a API do Chrome
 */
function moveFolder(folderId, newParentId, newIndex) {
  // Verificar se não está tentando mover para dentro de si mesmo
  if (folderId === newParentId) {
    showActionFeedback("Não é possível mover uma pasta para dentro dela mesma", "error");
    return;
  }
  
  // Verificar se não está tentando mover para dentro de um descendente
  chrome.bookmarks.getSubTree(folderId, (results) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao verificar árvore:", chrome.runtime.lastError);
      return;
    }
    
    // Função recursiva para verificar se o destino é descendente
    function isDescendant(node, targetId) {
      if (node.id === targetId) return true;
      if (node.children) {
        for (const child of node.children) {
          if (isDescendant(child, targetId)) return true;
        }
      }
      return false;
    }
    
    // Se o destino for descendente, abortar
    if (results[0] && isDescendant(results[0], newParentId)) {
      showActionFeedback("Não é possível mover uma pasta para dentro de sua subpasta", "error");
      return;
    }
    
    // Prosseguir com a movimentação
    const moveProperties = { parentId: newParentId };
    
    // Adicionar índice apenas se especificado
    if (newIndex !== undefined) {
      moveProperties.index = newIndex;
    }
    
    chrome.bookmarks.move(folderId, moveProperties, () => {
      if (chrome.runtime.lastError) {
        console.error("Erro ao mover pasta:", chrome.runtime.lastError);
        showActionFeedback("Erro ao mover pasta", "error");
        return;
      }

      console.log("Pasta movida com sucesso");

      // Recarregar a árvore de pastas
      if (typeof loadBookmarkFolders === 'function') {
        loadBookmarkFolders();
      } else {
        // Fallback: recarregar a página inteira
        chrome.bookmarks.getTree((tree) => {
          const roots = tree[0].children;
          populateFolderCheckboxes(roots, folderCheckboxesContainer);
          updateFolderCount();
        });
      }

      // Atualizar UI se a pasta movida ou destino estiver selecionada
      if (selectedFolderIds.has(folderId) || selectedFolderIds.has(newParentId)) {
        if (typeof reloadSelectedFolders === 'function') {
          reloadSelectedFolders();
        } else if (typeof window.reloadSelectedFolders === 'function') {
          window.reloadSelectedFolders();
        }
      }

      showActionFeedback("Pasta movida com sucesso", "success");
    });
  });
}