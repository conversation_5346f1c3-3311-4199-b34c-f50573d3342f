/**
 * Sistema de Seleção de Favoritos
 * Gerencia toda a lógica de seleção, incluindo checkboxes, seleção múltipla,
 * clique direito, seleção com Shift e operações em favoritos selecionados
 */

// Variáveis globais para seleção de favoritos
let lastClickedBookmarkCheckbox = null;
const selectedBookmarkIds = new Set();

// Tornar variáveis acessíveis globalmente
window.lastClickedBookmarkCheckbox = lastClickedBookmarkCheckbox;
window.selectedBookmarkIds = selectedBookmarkIds;

/**
 * Inicializa o sistema de seleção de favoritos
 */
function initBookmarkSelection() {
  console.log("Inicializando sistema de seleção de favoritos...");
  
  // Configurar botões de seleção
  setupSelectionButtons();
  
  // Configurar seleção com Shift
  setupBookmarkShiftSelection();
  
  // Configurar clique direito para seleção
  configureBookmarkRightClickSelection();
  
  // Inicializar contador
  updateSelectedBookmarksCount();
}

/**
 * Configura os botões de selecionar/desselecionar todos
 */
function setupSelectionButtons() {
  // Botão "Selecionar todos"
  const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
  if (selectAllBtn) {
    selectAllBtn.addEventListener("click", () => {
      toggleAllBookmarks(true);
    });
  }

  // Botão "Desselecionar todos"
  const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");
  if (deselectAllBtn) {
    deselectAllBtn.addEventListener("click", () => {
      toggleAllBookmarks(false);
    });
  }
}

/**
 * Atualiza o contador de favoritos selecionados
 */
function updateSelectedBookmarksCount() {
  const selectedCountEl = document.getElementById("selectedBookmarksCount");
  if (selectedCountEl) {
    selectedCountEl.textContent = `Selecionados: ${selectedBookmarkIds.size}`;
    
    // Adiciona classe visual para destacar quando há favoritos selecionados
    if (selectedBookmarkIds.size > 0) {
      selectedCountEl.classList.add("has-selected");
    } else {
      selectedCountEl.classList.remove("has-selected");
    }
  }
}

/**
 * Seleciona ou desseleciona todos os favoritos visíveis
 * @param {boolean} select - true para selecionar, false para desselecionar
 */
function toggleAllBookmarks(select) {
  const visibleCheckboxes = Array.from(document.querySelectorAll('.bookmark-checkbox'))
    .filter(checkbox => {
      const bookmarkItem = checkbox.closest('.bookmark-item');
      return bookmarkItem && bookmarkItem.style.display !== 'none';
    });
  
  if (visibleCheckboxes.length === 0) {
    showActionFeedback("Nenhum favorito visível para selecionar", "info");
    return;
  }
  
  // Captura quantos estavam selecionados antes de desmarcar
  let prevSelected = 0;
  if (!select) {
    prevSelected = visibleCheckboxes.filter(checkbox => checkbox.checked).length;
  }
  
  visibleCheckboxes.forEach(checkbox => {
    checkbox.checked = select;
    
    // Atualizar o estado de seleção
    const bookmarkItem = checkbox.closest('.bookmark-item');
    const bookmarkId = bookmarkItem.dataset.id;
    
    if (select) {
      selectedBookmarkIds.add(bookmarkId);
      bookmarkItem.classList.add("selected");
    } else {
      selectedBookmarkIds.delete(bookmarkId);
      bookmarkItem.classList.remove("selected");
    }
  });
  
  // Se alguma checkbox foi selecionada, atualizar o último clicado
  if (visibleCheckboxes.length > 0) {
    lastClickedBookmarkCheckbox = visibleCheckboxes[0];
    window.lastClickedBookmarkCheckbox = lastClickedBookmarkCheckbox;
  }
  
  updateSelectedBookmarksCount();
  
  // Mostrar feedback visual
  if (select) {
    showActionFeedback(`${visibleCheckboxes.length} favoritos selecionados`, "success");
  } else {
    showActionFeedback(`${prevSelected} favoritos desmarcados`, "info");
  }
}

/**
 * Configura a seleção de favoritos com clique direito do mouse
 */
function configureBookmarkRightClickSelection() {
  const bookmarksContainer = document.getElementById("bookmarksContainer");
  if (!bookmarksContainer) return;

  bookmarksContainer.addEventListener("contextmenu", (e) => {
    console.log("Selection.js: Clique direito detectado em", e.target);

    // Encontrar o elemento de bookmark mais próximo
    const bookmarkItem = e.target.closest(".bookmark-item");
    if (!bookmarkItem) {
      console.log("Selection.js: Bookmark item não encontrado");
      return;
    }

    console.log("Selection.js: Processando clique direito no bookmark", bookmarkItem.dataset.id);

    // Impedir o menu de contexto padrão
    e.preventDefault();

    // Encontrar o checkbox dentro do bookmark item
    const checkbox = bookmarkItem.querySelector(".bookmark-checkbox");
    if (!checkbox) {
      console.log("Selection.js: Checkbox não encontrado");
      return;
    }

    // Inverter o estado do checkbox
    checkbox.checked = !checkbox.checked;
    console.log("Selection.js: Checkbox alterado para", checkbox.checked);

    // Atualizar seleção visual
    updateBookmarkSelection(bookmarkItem, checkbox.checked);

    // Atualizar contador de seleção
    updateSelectedBookmarksCount();

    // Atualizar o último bookmark clicado para suporte ao Shift
    lastClickedBookmarkCheckbox = checkbox;
    window.lastClickedBookmarkCheckbox = checkbox;
    console.log("Selection.js: Clique direito processado com sucesso");
  });
}

/**
 * Atualiza o estado de seleção de um favorito
 * @param {HTMLElement} bookmarkItem - Elemento do favorito
 * @param {boolean} selected - Estado de seleção
 */
function updateBookmarkSelection(bookmarkItem, selected) {
  const bookmarkId = bookmarkItem.dataset.id;
  
  if (selected) {
    selectedBookmarkIds.add(bookmarkId);
    bookmarkItem.classList.add("selected", "sortable-selected");
  } else {
    selectedBookmarkIds.delete(bookmarkId);
    bookmarkItem.classList.remove("selected", "sortable-selected");
  }
}

/**
 * Configura a seleção de favoritos usando a tecla Shift
 */
function setupBookmarkShiftSelection() {
  const bookmarksContainer = document.getElementById("bookmarksContainer");
  if (!bookmarksContainer) return;
  
  bookmarksContainer.addEventListener("click", (e) => {
    // Verificar se é um checkbox de favorito
    if (e.target.classList.contains("bookmark-checkbox")) {
      const currentCheckbox = e.target;
      
      // Se o Shift está pressionado e há um checkbox anterior clicado
      if (e.shiftKey && lastClickedBookmarkCheckbox) {
        // Obter todos os checkboxes visíveis
        const checkboxes = Array.from(document.querySelectorAll(".bookmark-checkbox"))
          .filter(cb => {
            // Verificar se o checkbox está visível
            const container = cb.closest(".bookmark-item");
            return container && window.getComputedStyle(container).display !== "none";
          });
        
        // Encontrar índices do checkbox atual e do último clicado
        const currentIndex = checkboxes.indexOf(currentCheckbox);
        const lastIndex = checkboxes.indexOf(lastClickedBookmarkCheckbox);
        
        if (currentIndex !== -1 && lastIndex !== -1) {
          // Determinar o range de seleção
          const startIndex = Math.min(currentIndex, lastIndex);
          const endIndex = Math.max(currentIndex, lastIndex);
          
          // Determinar o estado de seleção baseado no checkbox atual
          const selectState = currentCheckbox.checked;
          
          // Aplicar o estado a todos os checkboxes no range
          for (let i = startIndex; i <= endIndex; i++) {
            const checkbox = checkboxes[i];
            const bookmarkItem = checkbox.closest('.bookmark-item');
            
            if (bookmarkItem) {
              checkbox.checked = selectState;
              updateBookmarkSelection(bookmarkItem, selectState);
            }
          }
          
          updateSelectedBookmarksCount();
        }
      }
      
      // Atualizar o último checkbox clicado
      lastClickedBookmarkCheckbox = currentCheckbox;
      window.lastClickedBookmarkCheckbox = currentCheckbox;
    }
  });
}

/**
 * Limpa todas as seleções de favoritos
 */
function clearBookmarkSelection() {
  selectedBookmarkIds.clear();
  
  // Remover classes visuais de seleção
  document.querySelectorAll('.bookmark-item.selected').forEach(item => {
    item.classList.remove('selected', 'sortable-selected');
  });
  
  // Desmarcar todos os checkboxes
  document.querySelectorAll('.bookmark-checkbox:checked').forEach(checkbox => {
    checkbox.checked = false;
  });
  
  updateSelectedBookmarksCount();
}

/**
 * Obtém os IDs dos favoritos selecionados
 * @returns {Array} Array com os IDs dos favoritos selecionados
 */
function getSelectedBookmarkIds() {
  return Array.from(selectedBookmarkIds);
}

/**
 * Obtém os elementos DOM dos favoritos selecionados
 * @returns {Array} Array com os elementos DOM dos favoritos selecionados
 */
function getSelectedBookmarkElements() {
  return getSelectedBookmarkIds().map(id => 
    document.querySelector(`.bookmark-item[data-id="${id}"]`)
  ).filter(element => element !== null);
}

/**
 * Verifica se há favoritos selecionados
 * @returns {boolean} true se há favoritos selecionados
 */
function hasSelectedBookmarks() {
  return selectedBookmarkIds.size > 0;
}

/**
 * Seleciona favoritos por IDs
 * @param {Array} bookmarkIds - Array de IDs de favoritos para selecionar
 */
function selectBookmarksByIds(bookmarkIds) {
  bookmarkIds.forEach(id => {
    const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
    if (bookmarkItem) {
      const checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
      if (checkbox) {
        checkbox.checked = true;
        updateBookmarkSelection(bookmarkItem, true);
      }
    }
  });
  
  updateSelectedBookmarksCount();
}
