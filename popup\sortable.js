// sortable.js

/**
 * Função desabilitada - Sistema de drag & drop removido completamente
 */
function initializeSortable() {
  console.log("Sortable.js: Sistema de drag & drop desabilitado - função vazia");
  // Sistema de drag & drop removido completamente
  return;
}

/**
 * Sincroniza a ordem dos elementos no DOM com a bookmark tree
 * @param {string} folderId - ID da pasta
 */
function syncDOMWithBookmarkTree(folderId) {
  // Salvar quais itens estavam selecionados antes da atualização
  const selectedIds = new Set(selectedBookmarkIds);

  chrome.bookmarks.getChildren(folderId, (bookmarks) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao obter favoritos:", chrome.runtime.lastError);
      return;
    }

    // Criar um mapa de ID -> índice atualizado antes de atualizar a visualização
    const indexMap = new Map();
    bookmarks.forEach((bookmark, index) => {
      indexMap.set(bookmark.id, index);
    });

    // Atualizar os índices de todos os elementos no DOM antes de reordenar
    const elementsToUpdate = document.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`);
    elementsToUpdate.forEach(element => {
      const id = element.dataset.id;
      if (id && indexMap.has(id)) {
        const newIndex = indexMap.get(id);
        if (element.dataset.index !== String(newIndex)) {
          console.log(`[syncDOM] Atualizando índice do elemento ${id}: ${element.dataset.index} -> ${newIndex}`);
          element.dataset.index = newIndex;
          
          // Atualizar também no cache
          if (bookmarkElementCache.has(id)) {
            const cachedElement = bookmarkElementCache.get(id);
            cachedElement.dataset.index = newIndex;
          }
        }
      }
    });
    
    // Atualizar a visualização com base na ordem atual da árvore
    updateFolderSelectively(folderId);
    
    // Após a atualização, restaurar a seleção dos itens
    setTimeout(() => {
      selectedIds.forEach(id => {
        const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
        if (bookmarkItem) {
          bookmarkItem.classList.add("sortable-selected", "selected");
          const checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
          if (checkbox) checkbox.checked = true;
        }
      });
      
      // Atualizar contador de selecionados
      updateSelectedBookmarksCount();
    }, 50);
  });
}

/**
 * Sincroniza a bookmark tree com a ordem dos elementos no DOM
 * @param {string} folderId - ID da pasta
 * @param {HTMLElement} container - Container dos favoritos
 */
function syncBookmarkTreeWithDOM(folderId, container) {
  // Salvar quais itens estavam selecionados antes da atualização
  const selectedIds = new Set(selectedBookmarkIds);
  
  // Obter todos os itens desta pasta no DOM
  const folderItems = Array.from(container.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`));
  
  // Se não há itens ou apenas um, não precisa sincronizar
  if (folderItems.length <= 1) {
    return;
  }
  
  // Obter os IDs dos favoritos na ordem atual do DOM
  const bookmarkIds = folderItems.map(item => item.dataset.id);
  
  // Obter a árvore atual de favoritos
  chrome.bookmarks.getChildren(folderId, (bookmarks) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao obter favoritos:", chrome.runtime.lastError);
      return;
    }
    
    // Filtrar apenas favoritos com URL, ordem é importante aqui
    const urlBookmarks = bookmarks.filter(bm => bm.url);
    
    // Criar mapa de ID -> índice atual da API do Chrome 
    const currentIndexMap = new Map();
    urlBookmarks.forEach((bookmark, index) => {
      currentIndexMap.set(bookmark.id, index);
    });
    
    // Atualizar os atributos data-index com os valores atuais
    folderItems.forEach(item => {
      const id = item.dataset.id;
      if (id && currentIndexMap.has(id)) {
        const currentIndex = currentIndexMap.get(id);
        if (item.dataset.index !== String(currentIndex)) {
          console.log(`[syncDOMWithTree] Atualizando índice de ${id}: ${item.dataset.index} -> ${currentIndex}`);
          item.dataset.index = currentIndex;
          
          // Atualizar também no cache de elementos
          if (bookmarkElementCache.has(id)) {
            const cachedElement = bookmarkElementCache.get(id);
            cachedElement.dataset.index = currentIndex;
          }
        }
      }
    });
    
    // Comparar as ordens atual e desejada
    let orderChanged = false;
    for (let i = 0; i < bookmarkIds.length; i++) {
      if (i < urlBookmarks.length && bookmarkIds[i] !== urlBookmarks[i].id) {
        orderChanged = true;
        break;
      }
    }
    
    // Se a ordem não mudou, não fazer nada
    if (!orderChanged) {
      console.log("A ordem já está correta, nenhuma ação necessária");
      return;
    }
    
    // Função para mover sequencialmente os favoritos (evita condições de corrida)
    function moveSequentially(index) {
      if (index >= bookmarkIds.length) {
        console.log(`Ordem sincronizada com sucesso para a pasta ${folderId}`);
        
        // Após reorganizar, atualizar a visualização mantendo seleção
        setTimeout(() => {
          updateFolderSelectively(folderId, true);
          
          // Restaurar a seleção dos itens após a atualização
          selectedIds.forEach(id => {
            const bookmarkItem = container.querySelector(`.bookmark-item[data-id="${id}"]`);
            if (bookmarkItem) {
              bookmarkItem.classList.add("sortable-selected", "selected");
              const checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
              if (checkbox) checkbox.checked = true;
              selectedBookmarkIds.add(id);
            }
          });
          
          // Atualizar contador de selecionados
          updateSelectedBookmarksCount();
          
          // Notificar que a sincronização está completa
          document.dispatchEvent(new CustomEvent('bookmarkSyncComplete', {
            detail: { folderId: folderId }
          }));
        }, 50);
        
        return;
      }
      
      const id = bookmarkIds[index];
      
      chrome.bookmarks.move(id, { index: index }, (result) => {
        if (chrome.runtime.lastError) {
          console.error(`Erro ao mover favorito ${id}:`, chrome.runtime.lastError);
          // Continuar com o próximo mesmo em caso de erro
          setTimeout(() => moveSequentially(index + 1), 50);
        } else if (result) {
          // Atualizar explicitamente o atributo data-index no elemento DOM
          const bookmarkItem = container.querySelector(`.bookmark-item[data-id="${id}"]`);
          if (bookmarkItem) {
            console.log(`[moveSequentially] Atualizando índice de ${id}: ${bookmarkItem.dataset.index} -> ${result.index}`);
            bookmarkItem.dataset.index = result.index;
            
            // Atualizar também no cache
            if (bookmarkElementCache.has(id)) {
              const cachedElement = bookmarkElementCache.get(id);
              cachedElement.dataset.index = result.index;
            }
          }
          
          // Mover o próximo favorito após um pequeno delay para garantir ordem correta
          setTimeout(() => moveSequentially(index + 1), 50);
        }
      });
    }
    
    // Iniciar o processo de movimentação sequencial
    moveSequentially(0);
  });
}

/**
 * Sincroniza a seleção de checkboxes com a seleção do Sortable 
 * @param {Event} event - Evento de mudança de checkbox
 */
function syncCheckboxWithSortable(event) {
  // Determinar se é um evento de checkbox ou clique
  let checkbox, bookmarkItem;

  if (event.target.classList.contains('bookmark-checkbox')) {
    // Evento direto do checkbox
    checkbox = event.target;
    bookmarkItem = checkbox.closest('.bookmark-item');
  } else {
    // Evento de clique em outro elemento
    bookmarkItem = event.target.closest('.bookmark-item');
    if (!bookmarkItem) return;
    checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
    if (!checkbox) return;

    // Alternar estado do checkbox
    checkbox.checked = !checkbox.checked;
  }

  if (!bookmarkItem || !checkbox) return;
  
  // Se o checkbox foi marcado, adiciona classe do Sortable.js
  if (checkbox.checked) {
    bookmarkItem.classList.add('sortable-selected');
    bookmarkItem.classList.add('selected'); // Adiciona classe para estilo visual
    selectedBookmarkIds.add(bookmarkItem.dataset.id);
  } else {
    bookmarkItem.classList.remove('sortable-selected');
    bookmarkItem.classList.remove('selected');
    selectedBookmarkIds.delete(bookmarkItem.dataset.id);
  }
  
  // Atualiza contador de forma segura
  if (typeof updateSelectedBookmarksCount === 'function') {
    updateSelectedBookmarksCount();
  } else {
    // Fallback caso a função não esteja disponível
    const selectedCountEl = document.getElementById("selectedBookmarksCount");
    if (selectedCountEl) {
      selectedCountEl.textContent = `Selecionados: ${selectedBookmarkIds.size}`;
      
      if (selectedBookmarkIds.size > 0) {
        selectedCountEl.classList.add("has-selected");
      } else {
        selectedCountEl.classList.remove("has-selected");
      }
    }
  }
}

/**
 * Sincroniza a seleção do Sortable com os checkboxes
 * @param {HTMLElement} element - Elemento que foi selecionado/deselecionado
 * @param {boolean} selected - Se o elemento está selecionado ou não
 */
function syncSortableWithCheckbox(element, selected) {
  if (!element || !element.classList || !element.dataset) {
    console.warn("Elemento inválido passado para syncSortableWithCheckbox");
    return;
  }
  
  const checkbox = element.querySelector('.bookmark-checkbox');
  
  if (!checkbox) return;
  
  // Validar o ID do bookmark
  const bookmarkId = element.dataset.id;
  if (!bookmarkId || bookmarkId === "undefined" || bookmarkId === "null") {
    console.warn("ID inválido encontrado em syncSortableWithCheckbox:", bookmarkId);
    return;
  }
  
  // Atualiza o checkbox sem disparar eventos
  if (checkbox.checked !== selected) {
    checkbox.checked = selected;
    
    // Atualiza o Set de ids selecionados
    if (selected) {
      selectedBookmarkIds.add(bookmarkId);
    } else {
      selectedBookmarkIds.delete(bookmarkId);
    }
  }
}

// Exporta as funções para uso em outros arquivos
window.syncCheckboxWithSortable = syncCheckboxWithSortable;
window.syncSortableWithCheckbox = syncSortableWithCheckbox;